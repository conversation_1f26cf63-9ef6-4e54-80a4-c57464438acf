import React, { ReactElement, ReactNode, cloneElement, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

export interface FormItemProps {
  /**
   * Tên của field trong form (chỉ bắt buộc khi sử dụng trong Form context)
   */
  name?: string;

  /**
   * Label hiển thị cho field
   */
  label?: ReactNode;

  /**
   * Nội dung của FormItem (thường là Input, Select, etc.)
   */
  children: ReactElement;

  /**
   * Text hiển thị bên dưới field để giải thích thêm
   */
  helpText?: ReactNode;

  /**
   * Field có bắt buộc hay không
   */
  required?: boolean;

  /**
   * Class bổ sung cho container
   */
  className?: string;

  /**
   * Class bổ sung cho label
   */
  labelClassName?: string;

  /**
   * Class bổ sung cho error message
   */
  errorClassName?: string;

  /**
   * Class bổ sung cho help text
   */
  helpTextClassName?: string;

  /**
   * Hiển thị label inline với input
   */
  inline?: boolean;

  /**
   * Ẩn error message
   */
  hideError?: boolean;

  /**
   * Tooltip cho label
   */
  tooltip?: ReactNode;

  /**
   * Mô tả cho field
   */
  description?: ReactNode;

  /**
   * Nội dung hiển thị trước input
   */
  prefix?: ReactNode;

  /**
   * Nội dung hiển thị sau input
   */
  suffix?: ReactNode;

  /**
   * Vị trí của label
   * @default 'top'
   */
  labelPosition?: 'top' | 'left' | 'right';

  /**
   * Animation khi hiển thị lỗi
   * @default 'none'
   */
  errorAnimation?: 'none' | 'fadeIn' | 'slideDown';

  /**
   * Thông báo thành công
   */
  successMessage?: ReactNode;

  /**
   * Trạng thái validation
   */
  validateStatus?: 'success' | 'warning' | 'error' | 'validating';

  /**
   * Kích thước của field
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Hiển thị dấu hai chấm sau label
   * @default false
   */
  colon?: boolean;

  /**
   * Hiển thị dấu sao cho field bắt buộc
   * @default false
   */
  asterisk?: boolean;

  /**
   * Class bổ sung cho description
   */
  descriptionClassName?: string;

  /**
   * Class bổ sung cho success message
   */
  successClassName?: string;

  /**
   * Class bổ sung cho prefix
   */
  prefixClassName?: string;

  /**
   * Class bổ sung cho suffix
   */
  suffixClassName?: string;
}

/**
 * FormItem component bọc các input field với label, error message, và help text
 *
 * @example
 * <FormItem name="email" label="Email" required>
 *   <Input type="email" placeholder="Nhập email" />
 * </FormItem>
 */
const FormItem: React.FC<FormItemProps> = ({
  name,
  label,
  children,
  helpText,
  required = false,
  className = '',
  labelClassName = '',
  errorClassName = '',
  helpTextClassName = '',
  inline = false,
  hideError = false,
  tooltip,
  description,
  prefix,
  suffix,
  labelPosition = 'top',
  errorAnimation = 'none',
  successMessage,
  validateStatus,
  size = 'md',
  colon = false,
  asterisk = false,
  descriptionClassName = '',
  successClassName = '',
  prefixClassName = '',
  suffixClassName = '',
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  // Kiểm tra xem có đang sử dụng trong Form context không
  const formContext = useFormContext();
  const isInFormContext = !!formContext && name !== undefined;

  // Lấy error message cho field này nếu đang trong form context
  const errorMessage = isInFormContext ? formContext.formState.errors[name!]?.message : undefined;

  // Lấy error từ props nếu được truyền vào
  const errorFromProps = children?.props?.error;

  // Sử dụng error từ form context hoặc từ props
  const finalErrorMessage =
    errorMessage ||
    (errorFromProps ? children?.props?.helperText || 'This field has an error' : undefined);

  // Tính toán trạng thái validation cuối cùng
  const finalValidateStatus = validateStatus || (finalErrorMessage ? 'error' : undefined);

  // Kiểm tra xem có hiển thị success message không
  const showSuccessMessage = finalValidateStatus === 'success' && successMessage;

  // Tạo ID cho field để liên kết label
  // Kiểm tra xem children có phải là Controller không
  const isController =
    typeof children.type !== 'string' &&
    children.type &&
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (children.type as any).name === 'Controller';

  // Nếu là Controller, lấy name từ Controller
  const controllerName = isController ? children?.props?.name : undefined;

  // Sử dụng ID từ children nếu có, nếu không tạo ID mới
  const childId = children?.props?.id;
  const fieldId =
    childId || `field-${name || controllerName || Math.random().toString(36).substring(2, 9)}`;

  // Size classes
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  // Label position classes
  const isHorizontal = labelPosition === 'left' || labelPosition === 'right';
  const labelPositionClasses = {
    top: 'flex-col',
    left: 'flex-row items-start',
    right: 'flex-row-reverse items-start',
  };

  // Tạo class cho container
  const containerClass = [
    'form-item',
    inline && !isHorizontal ? 'flex flex-wrap items-center gap-x-2' : 'flex',
    labelPositionClasses[labelPosition],
    finalValidateStatus && `form-item--${finalValidateStatus}`,
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo class cho label
  const labelClass = [
    sizeClasses[size],
    'font-medium text-foreground',
    labelPosition === 'top' ? 'block mb-1' : 'flex-shrink-0',
    labelPosition === 'left' ? 'mr-2 min-w-0' : '',
    labelPosition === 'right' ? 'ml-2 min-w-0' : '',
    required && asterisk ? 'required' : '',
    labelClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo class cho error message với animation
  const errorAnimationClasses = {
    none: '',
    fadeIn: 'animate-fadeIn',
    slideDown: 'animate-slideDown',
  };

  const errorClass = [
    'mt-1',
    sizeClasses[size],
    'text-error',
    errorAnimationClasses[errorAnimation],
    errorClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo class cho help text
  const helpTextClass = [
    sizeClasses[size],
    'text-muted',
    inline && !isHorizontal ? 'ml-2' : 'mt-1',
    helpTextClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo class cho description
  const descriptionClass = [
    sizeClasses[size],
    'text-muted mt-1',
    descriptionClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo class cho success message
  const successClass = [
    'mt-1',
    sizeClasses[size],
    'text-success',
    successClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Tạo class cho prefix/suffix
  const prefixClass = [
    'flex items-center',
    prefixClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const suffixClass = [
    'flex items-center',
    suffixClassName,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={containerClass} data-field-id={fieldId}>
      {label && (
        <div className={labelPosition === 'top' ? 'w-full' : 'flex-shrink-0'}>
          <label
            htmlFor={fieldId}
            className={`${labelClass} cursor-pointer relative inline-flex items-center`}
            onClick={() => {
              // Tìm input element và focus vào nó
              const inputElement = document.getElementById(fieldId);
              if (inputElement) {
                inputElement.focus();
              } else if (isController) {
                // Nếu không tìm thấy input với ID đã cho và children là Controller
                // Tìm tất cả các input trong form item và focus vào input đầu tiên
                const formItemElement = document.querySelector(`[data-field-id="${fieldId}"]`);
                if (formItemElement) {
                  const inputElements = formItemElement.querySelectorAll('input, textarea, select');
                  if (inputElements.length > 0) {
                    (inputElements[0] as HTMLElement).focus();
                  }
                }
              }
            }}
            onMouseEnter={() => tooltip && setShowTooltip(true)}
            onMouseLeave={() => tooltip && setShowTooltip(false)}
          >
            {label}
            {colon && <span className="ml-1">:</span>}
            {required && asterisk && <span className="text-error ml-1">*</span>}

            {/* Tooltip */}
            {tooltip && showTooltip && (
              <div className="absolute bottom-full left-0 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg whitespace-nowrap z-10">
                {tooltip}
                <div className="absolute top-full left-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            )}
          </label>

          {/* Description */}
          {description && (
            <div className={descriptionClass}>{description}</div>
          )}
        </div>
      )}

      <div className={labelPosition === 'top' ? 'w-full' : 'flex-1'}>
        {/* Input wrapper với prefix/suffix */}
        <div className="relative flex items-center">
          {prefix && (
            <div className={prefixClass}>
              {prefix}
            </div>
          )}

          <div className="flex-1">
            {isInFormContext ? (
              <Controller
                control={formContext.control}
                name={name!}
                render={({ field }) => {
                  // Clone children element và truyền các props cần thiết
                  return cloneElement(children, {
                    id: fieldId,
                    ...field,
                    ...(children?.props || {}),
                    // Nếu children đã có onChange handler, gọi cả handler đó và field.onChange
                    onChange: (e: React.ChangeEvent<HTMLInputElement> | unknown) => {
                      field.onChange(e);
                      if (children?.props?.onChange) {
                        children.props.onChange(e);
                      }
                    },
                    // Nếu children đã có onBlur handler, gọi cả handler đó và field.onBlur
                    onBlur: (e: React.FocusEvent<HTMLInputElement> | unknown) => {
                      field.onBlur();
                      if (children?.props?.onBlur) {
                        children.props.onBlur(e);
                      }
                    },
                    // Truyền error message để hiển thị lỗi trên input
                    error: errorMessage || undefined,
                    // Truyền validate status
                    validateStatus: finalValidateStatus,
                    // Đảm bảo ref được truyền xuống để có thể focus
                    ref: (element: HTMLElement) => {
                      // Lưu ref vào field.ref nếu có
                      if (typeof field.ref === 'function') {
                        field.ref(element);
                      }
                      // Lưu ref vào children.ref nếu có
                      // eslint-disable-next-line @typescript-eslint/no-explicit-any
                      const childrenRef = (children as any).ref;
                      if (childrenRef) {
                        if (typeof childrenRef === 'function') {
                          childrenRef(element);
                        } else if (Object.prototype.hasOwnProperty.call(childrenRef, 'current')) {
                          childrenRef.current = element;
                        }
                      }
                    },
                  });
                }}
              />
            ) : (
              // Nếu không sử dụng trong form context, chỉ render children
              cloneElement(children, {
                id: fieldId,
                ...(children?.props || {}),
                // Truyền validate status
                validateStatus: finalValidateStatus,
                // Đảm bảo ref được truyền xuống để có thể focus
                ref: (element: HTMLElement) => {
                  // Lưu ref vào children.ref nếu có
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const childrenRef = (children as any).ref;
                  if (childrenRef) {
                    if (typeof childrenRef === 'function') {
                      childrenRef(element);
                    } else if (Object.prototype.hasOwnProperty.call(childrenRef, 'current')) {
                      childrenRef.current = element;
                    }
                  }
                },
              })
            )}
          </div>

          {suffix && (
            <div className={suffixClass}>
              {suffix}
            </div>
          )}
        </div>
      </div>

      {/* Messages container */}
      <div className={labelPosition === 'top' ? 'w-full' : 'flex-1'}>
        {/* Success message */}
        {showSuccessMessage && (
          <div className={successClass}>{successMessage}</div>
        )}

        {/* Error message */}
        {!hideError && finalErrorMessage && (
          <div className={errorClass}>{String(finalErrorMessage)}</div>
        )}

        {/* Help text */}
        {helpText && !showSuccessMessage && !finalErrorMessage && (
          <div className={helpTextClass}>{helpText}</div>
        )}

        {/* Validating indicator */}
        {finalValidateStatus === 'validating' && (
          <div className={`mt-1 ${sizeClasses[size]} text-muted flex items-center`}>
            <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-2"></div>
            Validating...
          </div>
        )}
      </div>
    </div>
  );
};

export default FormItem;
