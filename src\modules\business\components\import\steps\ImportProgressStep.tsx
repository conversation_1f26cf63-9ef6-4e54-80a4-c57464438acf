import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Icon,
} from '@/shared/components/common';
import {
  ExcelData,
  ColumnMapping,
} from '../../../types/customer-import.types';
import { CustomerService, CreateBulkUserConvertCustomerDto } from '../../../services/customer.service';

interface ImportProgressStepProps {
  excelData: ExcelData;
  mappings: ColumnMapping[];
  isComplete: boolean;
  onImportComplete: (importedCount: number, errorCount: number) => void;
  selectedRowKeys?: React.Key[];
  onClose?: () => void;
}

/**
 * Component cho bước import progress và kết quả
 */
const ImportProgressStep: React.FC<ImportProgressStepProps> = ({
  excelData,
  mappings,
  isComplete,
  onImportComplete,
  selectedRowKeys = [],
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State cho progress
  const [progress, setProgress] = useState(0);
  const [currentRow, setCurrentRow] = useState(0);
  const [importedCount, setImportedCount] = useState(0);
  const [errorCount, setErrorCount] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);
  const [isImporting, setIsImporting] = useState(false);

  // Transform data theo mappings cho API
  const transformDataForAPI = (row: (string | number | boolean | null)[]) => {
    const activeMappings = mappings.filter(m => m.customerField);
    const customerData: {
      name: string;
      phone: string;
      email?: string;
      platform?: string;
      timezone?: string;
      tags?: string[];
      metadata?: Array<{
        configId: string;
        value: string;
      }>;
    } = {
      name: '',
      phone: '',
    };

    const customFields: Array<{
      configId: string;
      value: string;
    }> = [];

    activeMappings.forEach(mapping => {
      const columnIndex = excelData.headers.indexOf(mapping.excelColumn);
      const value = row[columnIndex];

      switch (mapping.customerField) {
        case 'name':
          customerData.name = value ? String(value).trim() : '';
          break;
        case 'email':
          if (value && String(value).trim()) {
            customerData.email = String(value).trim();
          }
          break;
        case 'phone':
          customerData.phone = value ? String(value).trim() : '';
          break;
        case 'platform':
          if (value && String(value).trim()) {
            customerData.platform = String(value).trim();
          }
          break;
        case 'timezone':
          if (value && String(value).trim()) {
            customerData.timezone = String(value).trim();
          }
          break;
        case 'tags':
          if (value) {
            customerData.tags = String(value).split(',').map(tag => tag.trim()).filter(tag => tag);
          }
          break;
        default:
          // Custom fields - tất cả các trường khác đều là custom fields trong metadata
          if (value !== null && value !== undefined && value !== '') {
            customFields.push({
              configId: mapping.customerField,
              value: String(value),
            });
          }
          break;
      }
    });

    if (customFields.length > 0) {
      customerData.metadata = customFields;
    }

    return customerData;
  };

  // Import process với API thực tế
  const performImport = async () => {
    setIsImporting(true);
    const totalRows = excelData.rows.length;

    try {
      // Lọc chỉ những dòng được chọn
      const selectedRows = excelData.rows.filter((_, index) => {
        const rowIndex = index + 2; // +2 vì bắt đầu từ dòng 2 (sau header)
        return selectedRowKeys.length === 0 || selectedRowKeys.includes(rowIndex);
      });

      // Transform dữ liệu đã chọn
      const customers = selectedRows.map((row) => transformDataForAPI(row));

      // Validate dữ liệu cơ bản
      const validCustomers = customers.filter((customer, index) => {
        const originalIndex = selectedRowKeys.length > 0
          ? excelData.rows.findIndex(row => row === selectedRows[index])
          : index;
        const rowNumber = originalIndex + 2;

        if (!customer.name || customer.name.trim() === '') {
          setErrors(prev => [...prev, `Dòng ${rowNumber}: Thiếu tên khách hàng`]);
          return false;
        }
        if (!customer.phone || customer.phone.trim() === '') {
          setErrors(prev => [...prev, `Dòng ${rowNumber}: Thiếu số điện thoại`]);
          return false;
        }
        return true;
      });

      if (validCustomers.length === 0) {
        setIsImporting(false);
        onImportComplete(0, totalRows);
        return;
      }

      // Chuẩn bị request bulk create theo format API
      const bulkRequest: CreateBulkUserConvertCustomerDto = {
        customers: validCustomers,
        skipDuplicates: true,
        continueOnError: true,
      };

      // Gọi API bulk create
      setProgress(50);
      setCurrentRow(Math.floor(totalRows / 2));

      // Gọi API endpoint: POST /v1/user/convert-customers/bulk
      const response = await CustomerService.createBulkConvertCustomers(bulkRequest);

      // Cập nhật kết quả
      setProgress(100);
      setCurrentRow(totalRows);

      // Xử lý response từ API
      if (response && response.result) {
        const result = response.result;
        setImportedCount(result.successCount);
        setErrorCount(result.errorCount + result.skippedCount);

        // Xử lý lỗi chi tiết từ results
        const detailErrors: string[] = [];
        result.results.forEach((item) => {
          if (item.status === 'error' || item.status === 'skipped') {
            detailErrors.push(`Dòng ${item.index + 2}: ${item.message || 'Lỗi không xác định'}`);
          }
        });
        setErrors(detailErrors);

        onImportComplete(result.successCount, result.errorCount + result.skippedCount);
      } else {
        throw new Error(response.message || 'Import failed');
      }

      setIsImporting(false);

    } catch (error) {
      console.error('Error during import:', error);
      setIsImporting(false);
      setErrors([`Lỗi kết nối API: ${error instanceof Error ? error.message : 'Vui lòng thử lại.'}`]);
      onImportComplete(0, totalRows);
    }
  };

  // Start import when component mounts
  useEffect(() => {
    if (!isComplete && !isImporting) {
      performImport();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isComplete, isImporting]);

  // Render importing state
  if (!isComplete) {
    return (
      <div className="w-full space-y-6">
        <div className="p-6 bg-card rounded-lg">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="body2">
                {t('customer.import.progress.processing')}
              </Typography>
              <Typography variant="body2" className="text-muted">
                {currentRow} / {excelData.rows.length}
              </Typography>
            </div>

            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <Typography variant="h4" className="text-green-600">
                  {importedCount}
                </Typography>
                <Typography variant="body2" className="text-muted">
                  {t('customer.import.progress.imported')}
                </Typography>
              </div>
              <div>
                <Typography variant="h4" className="text-red-600">
                  {errorCount}
                </Typography>
                <Typography variant="body2" className="text-muted">
                  {t('customer.import.progress.errors')}
                </Typography>
              </div>
            </div>
          </div>
        </div>

        {errors.length > 0 && (
          <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
            <Typography variant="body2" className="font-medium text-red-700 mb-2">
              {t('customer.import.progress.recentErrors')}
            </Typography>
            <div className="max-h-32 overflow-y-auto">
              <ul className="space-y-1">
                {errors.slice(-5).map((error, index) => (
                  <li key={index} className="text-sm text-red-600">
                    • {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render complete state
  return (
    <div className="w-full space-y-6">
      <div className="w-full text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon name="check" size="lg" className="text-green-600" />
        </div>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 text-center bg-card rounded-lg">
          <Typography variant="h4" className="text-blue-600 mb-1">
            {excelData.rows.length}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('customer.import.complete.totalProcessed')}
          </Typography>
        </div>

        <div className="p-4 text-center border border-green-200 bg-green-50 rounded-lg">
          <Typography variant="h4" className="text-green-600 mb-1">
            {importedCount}
          </Typography>
          <Typography variant="body2" className="text-green-700">
            {t('customer.import.complete.successfullyImported')}
          </Typography>
        </div>

        <div className="p-4 text-center border border-red-200 bg-red-50 rounded-lg">
          <Typography variant="h4" className="text-red-600 mb-1">
            {errorCount}
          </Typography>
          <Typography variant="body2" className="text-red-700">
            {t('customer.import.complete.failed')}
          </Typography>
        </div>
      </div>

      {/* Error Details */}
      {errors.length > 0 && (
        <div className="p-4 bg-card rounded-lg">
          <Typography variant="body2" className="font-medium mb-2">
            {t('customer.import.complete.errorDetails')}
          </Typography>
          <div className="max-h-40 overflow-y-auto">
            <ul className="space-y-1">
              {errors.map((error, index) => (
                <li key={index} className="text-sm text-red-600">
                  • {error}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Next Steps */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <Typography variant="body2" className="font-medium text-blue-700 mb-2">
          {t('customer.import.complete.nextSteps')}
        </Typography>
        <ul className="space-y-1 text-sm text-blue-600">
          <li>• {t('customer.import.complete.reviewCustomers')}</li>
          <li>• {t('customer.import.complete.setupSegments')}</li>
          <li>• {t('customer.import.complete.createCampaigns')}</li>
        </ul>
      </div>


    </div>
  );
};

export default ImportProgressStep;
