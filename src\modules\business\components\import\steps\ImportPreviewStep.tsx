import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Table,
  Icon,
  Checkbox,
} from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  ExcelData,
  ColumnMapping,
  ImportedCustomerData,
} from '../../../types/customer-import.types';

interface ImportPreviewStepProps {
  excelData: ExcelData;
  mappings: ColumnMapping[];
  onSelectedRowsChange?: (selectedRowKeys: React.Key[]) => void;
  onStartImport?: () => void;
  onGoBack?: () => void;
}

/**
 * Component cho bước preview dữ liệu trước khi import
 */
const ImportPreviewStep: React.FC<ImportPreviewStepProps> = ({
  excelData,
  mappings,
  onSelectedRowsChange,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State cho import options
  const [skipInvalidRows, setSkipInvalidRows] = useState(true);
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);

  // State cho row selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Transform data theo mappings
  const transformedData = useMemo(() => {
    // Add null check for mappings and excelData
    if (!mappings || !Array.isArray(mappings) || !excelData || !excelData.rows) {
      return [];
    }
    const activeMappings = mappings.filter(m => m.customerField);

    return excelData.rows.map((row, index) => {
      const customerData: ImportedCustomerData = {
        rowIndex: index + 2, // +2 vì bắt đầu từ dòng 2 (sau header)
      };

      activeMappings.forEach(mapping => {
        const columnIndex = excelData.headers.indexOf(mapping.excelColumn);
        const value = row[columnIndex];

        switch (mapping.customerField) {
          case 'name':
            if (value && String(value).trim()) {
              customerData.name = String(value).trim();
            }
            break;
          case 'email':
            if (value && String(value).trim()) {
              customerData.email = String(value).trim();
            }
            break;
          case 'phone':
            if (value && String(value).trim()) {
              customerData.phone = String(value).trim();
            }
            break;
          case 'address':
            if (value && String(value).trim()) {
              customerData.address = String(value).trim();
            }
            break;
          case 'tags':
            if (value) {
              customerData.tags = String(value).split(',').map(tag => tag.trim()).filter(tag => tag);
            }
            break;
          default:
            // Custom fields
            if (!customerData.customFields) {
              customerData.customFields = {};
            }
            customerData.customFields[mapping.customerField] = value;
            break;
        }
      });

      return customerData;
    });
  }, [excelData, mappings]);

  // Validation data
  const validationResult = useMemo(() => {
    const validData: ImportedCustomerData[] = [];
    const invalidData: ImportedCustomerData[] = [];
    const errors: string[] = [];

    transformedData.forEach(data => {
      const rowErrors: string[] = [];

      // Validate required fields - Tên khách hàng bắt buộc
      if (!data.name || data.name.trim() === '') {
        rowErrors.push(t('business:customer.import.validation.nameRequired', 'Tên khách hàng là bắt buộc'));
      }

      // Validate required fields - Số điện thoại bắt buộc
      if (!data.phone || data.phone.trim() === '') {
        rowErrors.push(t('business:customer.import.validation.phoneRequired', 'Số điện thoại là bắt buộc'));
      }

      // Validate email format (nếu có)
      if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        rowErrors.push(t('business:customer.import.validation.invalidEmail', 'Email không hợp lệ'));
      }

      // Validate phone format (nếu có)
      if (data.phone && !/^[\d\s\-+()]+$/.test(data.phone)) {
        rowErrors.push(t('business:customer.import.validation.invalidPhone', 'Số điện thoại không hợp lệ'));
      }

      if (rowErrors.length > 0) {
        invalidData.push(data);
        errors.push(`Dòng ${data.rowIndex}: ${rowErrors.join(', ')}`);
      } else {
        validData.push(data);
      }
    });

    return {
      validData,
      invalidData,
      errors,
      totalRows: transformedData.length,
      validRows: validData.length,
      invalidRows: invalidData.length,
    };
  }, [transformedData, t]);

  // Cập nhật selected rows khi validation result thay đổi
  React.useEffect(() => {
    // Mặc định chọn tất cả các dòng hợp lệ
    const validRowKeys = validationResult.validData.map(item => item.rowIndex);
    setSelectedRowKeys(validRowKeys);
  }, [validationResult.validData]);

  // Gọi callback khi selectedRowKeys thay đổi
  React.useEffect(() => {
    onSelectedRowsChange?.(selectedRowKeys);
  }, [selectedRowKeys, onSelectedRowsChange]);

  // Columns cho preview table
  const previewColumns: TableColumn<ImportedCustomerData>[] = [
    {
      key: 'rowIndex',
      title: t('business:customer.import.preview.row', 'Dòng'),
      dataIndex: 'rowIndex',
      width: 80,
    },
    {
      key: 'name',
      title: (
        <span>
          {t('business:customer.form.name', 'Tên khách hàng')}
          <span className="text-red-500 ml-1">*</span>
        </span>
      ),
      dataIndex: 'name',
      render: (value: unknown) => {
        if (!value) {
          return <span className="text-red-500 font-medium">Thiếu tên</span>;
        }
        return String(value);
      },
    },
    {
      key: 'email',
      title: t('business:customer.form.email', 'Email'),
      dataIndex: 'email',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'phone',
      title: (
        <span>
          {t('business:customer.form.phone', 'Số điện thoại')}
          <span className="text-red-500 ml-1">*</span>
        </span>
      ),
      dataIndex: 'phone',
      render: (value: unknown) => {
        if (!value) {
          return <span className="text-red-500 font-medium">Thiếu SĐT</span>;
        }
        return String(value);
      },
    },
    {
      key: 'address',
      title: t('business:customer.form.address', 'Địa chỉ'),
      dataIndex: 'address',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'tags',
      title: t('business:customer.form.tags', 'Tags'),
      dataIndex: 'tags',
      render: (value: unknown) => {
        const tags = value as string[] | undefined;
        return tags && tags.length > 0 ? tags.join(', ') : <span className="text-muted">-</span>;
      },
    },
  ];

  return (
    <div className="w-full space-y-6">
      {/* Summary */}
      <ListOverviewCard
        items={[
          {
            title: t('business:customer.import.preview.totalRows', 'Tổng số dòng'),
            value: validationResult.totalRows,
            color: 'blue',
            description: 'Tổng số dòng dữ liệu'
          },
          {
            title: t('business:customer.import.preview.validRows', 'Dòng hợp lệ'),
            value: validationResult.validRows,
            color: 'green',
            description: 'Sẽ được import'
          },
          {
            title: t('business:customer.import.preview.invalidRows', 'Dòng lỗi'),
            value: validationResult.invalidRows,
            color: 'red',
            description: 'Sẽ bị bỏ qua (thiếu tên hoặc SĐT)'
          }
        ]}
        maxColumns={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3 }}
        gap={4}
      />

      {/* Validation Errors */}
      {validationResult.errors.length > 0 && (
        <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Icon name="alert-triangle" size="sm" className="text-orange-500 mt-0.5" />
            <div className="flex-1">
              <Typography variant="body2" className="font-medium text-orange-700 mb-2">
                {t('business:customer.import.preview.validationWarnings', 'Cảnh báo validation')}
              </Typography>
              <Typography variant="body2" className="text-orange-600 mb-2">
                Các dòng sau sẽ bị bỏ qua do thiếu thông tin bắt buộc (tên khách hàng và số điện thoại):
              </Typography>
              <div className="max-h-32 overflow-y-auto">
                <ul className="space-y-1">
                  {validationResult.errors.slice(0, 10).map((error, index) => (
                    <li key={index} className="text-sm text-orange-600">
                      • {error}
                    </li>
                  ))}
                  {validationResult.errors.length > 10 && (
                    <li className="text-sm text-orange-600">
                      ... và {validationResult.errors.length - 10} lỗi khác
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Table */}
      <div className="p-6 bg-card rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h6">
            {t('business:customer.import.preview.dataPreview', 'Hiển thị dữ liệu mẫu')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {t('business:customer.import.preview.selectedRows', 'Đã chọn')}: {selectedRowKeys.length}/{validationResult.validRows}
          </Typography>
        </div>
        <div className="overflow-x-auto">
          <Table
            columns={previewColumns}
            data={validationResult.validData}
            rowKey="rowIndex"
            pagination={true}
            size="sm"
            rowSelection={{
              selectedRowKeys: selectedRowKeys,
              onChange: (newSelectedRowKeys: React.Key[]) => {
                setSelectedRowKeys(newSelectedRowKeys);
              },
              getCheckboxProps: (record: ImportedCustomerData) => ({
                name: `row-${record.rowIndex}`,
              }),
            }}
          />
        </div>
      </div>

      {/* Import Options */}
      <div className="p-4 bg-card rounded-lg">
        <Typography variant="body2" className="font-medium mb-4">
          {t('customer.import.preview.importOptions')}
        </Typography>
        <div className="space-y-4">
          <div>
            <Checkbox
              label={t('customer.import.preview.skipInvalidRows')}
              checked={skipInvalidRows}
              onChange={setSkipInvalidRows}
            />
          </div>
          <div>
            <Checkbox
              label={t('customer.import.preview.sendWelcomeEmail')}
              checked={sendWelcomeEmail}
              onChange={setSendWelcomeEmail}
            />
          </div>
        </div>
      </div>


    </div>
  );
};

export default ImportPreviewStep;
