import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Icon,
} from '@/shared/components/common';
import { OrderItemDto } from '../../types/order.types';
import { DeliveryAddressDto } from '../../services/shipping-calculator.service';
import { ShippingCalculatorService } from '../../services/shipping-calculator.service';
import { formatCurrency } from '@/shared/utils/number-format.utils';
import { useAvailableCarriers } from '../../hooks/useAvailableCarriers';
import { useComboShipping, usePhysicalProductsForShipping } from '../../hooks/useComboShipping';

interface SingleCarrierCalculatorProps {
  shopId: number;
  customerId?: number;
  selectedItems: OrderItemDto[];
  deliveryAddress?: DeliveryAddressDto;
  preferredCarrier: 'GHN' | 'GHTK';
  onShippingCalculated: (carrier: string, fee: number, serviceType?: string, estimatedDeliveryTime?: string) => void;
  className?: string;
}

interface ShippingResult {
  fee: number;
  serviceType?: string;
  estimatedDeliveryTime?: string;
  error?: string;
}

/**
 * Component tính phí vận chuyển cho một carrier cụ thể
 */
const SingleCarrierCalculator: React.FC<SingleCarrierCalculatorProps> = ({
  shopId,
  customerId,
  selectedItems,
  deliveryAddress,
  preferredCarrier,
  onShippingCalculated,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Lấy thông tin carrier
  const { getCarrierByType } = useAvailableCarriers();
  const carrierInfo = getCarrierByType(preferredCarrier);

  // State
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<ShippingResult | null>(null);

  // Ref để lưu callback và tránh dependency loop
  const onShippingCalculatedRef = useRef(onShippingCalculated);
  onShippingCalculatedRef.current = onShippingCalculated;

  // Kiểm tra có sản phẩm cần vận chuyển không (bao gồm combo)
  const { needsShipping: hasPhysicalProducts, isLoading: checkingShipping } = useComboShipping(selectedItems);

  // Lấy danh sách sản phẩm vật lý để tính phí ship
  const { physicalProducts, isLoading: loadingPhysicalProducts } = usePhysicalProductsForShipping(selectedItems);

  // Tính phí vận chuyển
  const calculateShippingFee = useCallback(async () => {
    if (!deliveryAddress || !hasPhysicalProducts || selectedItems.length === 0) {
      return;
    }

    setIsCalculating(true);
    setResult(null);

    try {
      // Sử dụng danh sách sản phẩm vật lý thực tế (bao gồm từ combo)
      const productsForShipping = physicalProducts.length > 0
        ? physicalProducts
        : selectedItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
          }));

      const request = ShippingCalculatorService.createCalculateShippingRequest(
        shopId,
        productsForShipping,
        customerId,
        deliveryAddress,
        preferredCarrier
      );

      console.log(`🚚 [SingleCarrierCalculator] Calculating fee for ${preferredCarrier}:`, request);

      // Sử dụng API tính phí vận chuyển đúng
      const response = await ShippingCalculatorService.calculateShippingFee(request);

      // Kiểm tra response format - có thể là ApiResponseDto hoặc API trực tiếp
      const isApiResponseDto = 'code' in response && 'result' in response;
      const isDirectApi = 'success' in response && 'data' in response;

      let shippingData: Record<string, unknown> | null = null;
      let isSuccess = false;

      if (isApiResponseDto) {
        isSuccess = response.code === 0 && !!response.result;
        shippingData = response.result as unknown as Record<string, unknown>;
      } else if (isDirectApi) {
        isSuccess = !!(response as Record<string, unknown>)['success'] && !!(response as Record<string, unknown>)['data'];
        shippingData = (response as Record<string, unknown>)['data'] as Record<string, unknown>;
      }

      if (isSuccess && shippingData) {
        const shippingResult: ShippingResult = {
          fee: (shippingData['fee'] as number) || (shippingData['shippingFee'] as number),
          serviceType: shippingData['serviceType'] as string,
          estimatedDeliveryTime: shippingData['estimatedDeliveryTime'] as string,
        };

        setResult(shippingResult);

        // Callback với kết quả - sử dụng ref để tránh dependency loop
        onShippingCalculatedRef.current(
          preferredCarrier,
          shippingResult.fee,
          shippingResult.serviceType,
          shippingResult.estimatedDeliveryTime
        );

        console.log(`✅ [SingleCarrierCalculator] Calculated successfully:`, shippingResult);
      } else {
        const errorMessage = isApiResponseDto
          ? response.message || 'Calculation failed'
          : (response as Record<string, unknown>)['message'] || 'Calculation failed';
        throw new Error(typeof errorMessage === 'string' ? errorMessage : 'Calculation failed');
      }
    } catch (error) {
      console.error(`❌ [SingleCarrierCalculator] Error calculating ${preferredCarrier}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Calculation failed';
      setResult({ fee: 0, error: errorMessage });
    } finally {
      setIsCalculating(false);
    }
  }, [shopId, customerId, selectedItems, deliveryAddress, preferredCarrier, hasPhysicalProducts, physicalProducts]);

  // Auto-calculate khi có thay đổi với debounce
  useEffect(() => {
    if (deliveryAddress && hasPhysicalProducts && selectedItems.length > 0) {
      // Debounce để tránh gọi API quá nhiều lần
      const timeoutId = setTimeout(() => {
        calculateShippingFee();
      }, 500); // Đợi 500ms sau thay đổi cuối cùng

      return () => clearTimeout(timeoutId);
    }
    // Return undefined explicitly for cases where condition is not met
    return undefined;
  }, [calculateShippingFee, deliveryAddress, hasPhysicalProducts, selectedItems.length]);

  // Không hiển thị nếu không có sản phẩm cần vận chuyển
  if (!hasPhysicalProducts) {
    return null;
  }

  // Loading state khi đang kiểm tra shipping needs
  if (checkingShipping || loadingPhysicalProducts) {
    return (
      <div className={className}>
        <div className="flex items-center gap-3 p-4 bg-primary/10 border border-primary/20 rounded-lg">
          <Icon name="loader" size="sm" className="animate-spin text-primary" />
          <Typography variant="body2" className="text-primary">
            {t('business:order.checkingShippingNeeds', 'Đang kiểm tra nhu cầu vận chuyển...')}
          </Typography>
        </div>
      </div>
    );
  }

  // Không hiển thị nếu không có carrier info
  if (!carrierInfo) {
    return (
      <div className={className}>
        <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <div className="flex items-start space-x-3">
            <Icon name="alert-triangle" size="sm" className="text-destructive mt-0.5" />
            <div>
              <Typography variant="subtitle2" className="text-destructive font-medium">
                {t('business:order.carrierConfigError', 'Lỗi cấu hình nhà vận chuyển')}
              </Typography>
              <Typography variant="body2" className="text-destructive/80 mt-1">
                {t('business:order.carrierNotFound', 'Không tìm thấy cấu hình cho nhà vận chuyển {{carrier}}', { carrier: preferredCarrier })}
              </Typography>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">

        {/* Thông tin carrier được chọn */}
        <div className="p-4 bg-card border border-border rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`
                w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0
                ${preferredCarrier === 'GHN'
                  ? 'bg-blue-50 dark:bg-blue-900/20'
                  : 'bg-green-50 dark:bg-green-900/20'
                }
              `}>
                <Icon
                  name={carrierInfo.icon}
                  size="lg"
                  className={`
                    ${preferredCarrier === 'GHN'
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-green-600 dark:text-green-400'
                    }
                  `}
                />
              </div>
              <div>
                <Typography variant="subtitle1" className="font-semibold text-foreground">
                  {carrierInfo.displayName}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {carrierInfo.description}
                </Typography>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={calculateShippingFee}
              disabled={isCalculating}
              className="flex-shrink-0"
            >
              <Icon name="refresh-cw" size="sm" className="mr-2" />
              {t('business:order.recalculate', 'Tính lại')}
            </Button>
          </div>
        </div>

        {/* Loading state */}
        {isCalculating && (
          <div className="flex items-center gap-3 p-4 bg-primary/10 border border-primary/20 rounded-lg">
            <Icon name="loader" size="sm" className="animate-spin text-primary" />
            <Typography variant="body2" className="text-primary">
              {t('business:order.calculatingShippingFee', 'Đang tính phí vận chuyển...')}
            </Typography>
          </div>
        )}

        {/* Kết quả tính phí */}
        {!isCalculating && result && (
          <div>
            {result.error ? (
              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Icon name="alert-triangle" size="sm" className="text-destructive mt-0.5" />
                  <div>
                    <Typography variant="subtitle2" className="text-destructive font-medium">
                      {t('business:order.calculationError', 'Lỗi tính phí vận chuyển')}
                    </Typography>
                    <Typography variant="body2" className="text-destructive/80 mt-1">
                      {result.error.includes('Request failed with status code 400')
                        ? t('business:order.apiConnectionError', 'Lỗi kết nối API hoặc thông tin không hợp lệ')
                        : result.error}
                    </Typography>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-success/10 border border-success/20 rounded-lg">
                <div className="flex items-center gap-3 mb-4">
                  <Icon name="check-circle" size="sm" className="text-success" />
                  <Typography variant="subtitle2" className="text-success font-medium">
                    {t('business:order.shippingFeeCalculated', 'Phí vận chuyển đã được tính')}
                  </Typography>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Typography variant="body2" className="text-foreground font-medium">
                      {t('business:order.shippingFee', 'Phí vận chuyển')}:
                    </Typography>
                    <Typography variant="h6" className="text-foreground font-bold">
                      {formatCurrency(result.fee)}
                    </Typography>
                  </div>

                  {result.serviceType && (
                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.serviceType', 'Dịch vụ')}:
                      </Typography>
                      <Typography variant="body2" className="text-foreground font-medium">
                        {result.serviceType}
                      </Typography>
                    </div>
                  )}

                  {result.estimatedDeliveryTime && (
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.estimatedDeliveryTime', 'Thời gian giao hàng')}:
                      </Typography>
                      <div className="flex items-center gap-2">
                        <Icon name="clock" size="xs" className="text-muted-foreground" />
                        <Typography variant="body2" className="text-foreground font-medium">
                          {result.estimatedDeliveryTime}
                        </Typography>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SingleCarrierCalculator;
