import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { Controller } from 'react-hook-form';
import { z } from 'zod';
import { NotificationUtil } from '@/shared/utils/notification';
import { ProductDto, UpdateProductDto, PriceTypeEnum, HasPriceDto, ShipmentConfigDto } from '../../types/product.types';

// Extended ProductDto interface để bao gồm advancedInfo cho combo products
interface ComboProductDto extends ProductDto {
  advancedInfo?: {
    purchaseCount?: number;
    info?: Array<{
      productId: number;
      total: number;
    }>;
  };
}
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { ProductService } from '../../services/product.service';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useProduct, useUpdateProduct } from '../../hooks/useProductQuery';

interface ComboProductEditFormProps {
  productId: number;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Extended FileWithMetadata interface
interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string | undefined;
  name?: string | undefined;
  size?: number | undefined;
  type?: string | undefined;
  metadata?: {
    key?: string;
    url?: string;
    isExisting?: boolean;
  };
}

// Interface cho combo product item
interface ComboProductItem {
  productId: number;
  productName: string;
  quantity: number;
  discountPercent?: number;
}

// Interface cho upload URL item từ API response
interface UploadUrlItem {
  uploadUrl?: string;
  url?: string;
  key?: string;
  index?: number;
}

interface ComboProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: ExtendedFileWithMetadata[];
  // Shipping configuration
  shipmentConfig?: ShipmentConfigDto;
  // Combo product specific fields
  comboProducts: ComboProductItem[];
}

const ComboProductEditForm: React.FC<ComboProductEditFormProps> = ({
  productId,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Gọi API lấy chi tiết sản phẩm
  const { data: productData, isLoading: isLoadingProduct, error: productError } = useProduct(productId);

  // Cast product data to ComboProductDto
  const product = productData as ComboProductDto;

  // Hook để cập nhật sản phẩm
  const updateProductMutation = useUpdateProduct();

  // Schema cho media files
  const mediaFileSchema = z.object({
    id: z.string(),
    file: z.instanceof(File),
    preview: z.string().optional(),
    url: z.string().optional(),
    name: z.string().optional(),
    size: z.number().optional(),
    type: z.string().optional(),
    metadata: z.object({
      key: z.string().optional(),
      url: z.string().optional(),
      isExisting: z.boolean().optional(),
    }).optional(),
  });

  // Schema cho custom fields
  const customFieldSchema = z.object({
    id: z.number(),
    fieldId: z.number(),
    label: z.string(),
    component: z.string(),
    type: z.string(),
    required: z.boolean(),
    configJson: z.record(z.unknown()),
    value: z.record(z.unknown()),
  });

  // Schema cho shipment config
  const shipmentConfigSchema = z.object({
    lengthCm: z.union([z.string(), z.number()]).optional().transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    }),
    widthCm: z.union([z.string(), z.number()]).optional().transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    }),
    heightCm: z.union([z.string(), z.number()]).optional().transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    }),
    weightGram: z.union([z.string(), z.number()]).optional().transform((val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = typeof val === 'string' ? parseFloat(val) : val;
      return isNaN(num) ? undefined : num;
    }),
  }).optional();

  // Schema validation cho combo sản phẩm
  const comboProductSchema = z
    .object({
      name: z.string().min(1, 'Tên combo không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.array(mediaFileSchema).optional(),
      customFields: z.array(customFieldSchema).optional(),
      shipmentConfig: shipmentConfigSchema,
      // Combo product specific validations
      comboProducts: z
        .array(
          z.object({
            productId: z.number().min(1, 'ID sản phẩm không hợp lệ'),
            productName: z.string().min(1, 'Tên sản phẩm không được để trống'),
            quantity: z.number().min(1, 'Số lượng phải lớn hơn 0'),
            discountPercent: z.number().min(0).max(100).optional(),
          })
        )
        .min(1, 'Combo phải có ít nhất 1 sản phẩm'),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra combo phải có ít nhất 1 sản phẩm
      if (!data.comboProducts || data.comboProducts.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Combo phải có ít nhất 1 sản phẩm',
          path: ['comboProducts'],
        });
      }

      // Validation cho giá combo (chỉ HAS_PRICE, không có STRING_PRICE)
      if (!data.salePrice || data.salePrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá bán',
          path: ['salePrice'],
        });
      }

      if (!data.currency || data.currency.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng chọn đơn vị tiền tệ',
          path: ['currency'],
        });
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<ExtendedFileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State để track ảnh bị xóa
  const [deletedImages, setDeletedImages] = useState<Array<{ key: string; url: string }>>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho combo products
  const [comboProducts, setComboProducts] = useState<ComboProductItem[]>([]);

  // State cho loaded products (để cache)
  const [loadedProducts, setLoadedProducts] = useState<Map<string, ProductDto>>(new Map());

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Khởi tạo giá trị mặc định từ product data
  const defaultValues = useMemo(() => {
    if (!product) return {
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      shipmentConfig: undefined,
      // Combo product defaults
      comboProducts: [],
    };

    const price = product.price as HasPriceDto;

    // Extract combo-specific data from advancedInfo
    const comboItems = product.advancedInfo?.info || [];

    return {
      name: product.name || '',
      typePrice: PriceTypeEnum.HAS_PRICE, // Combo luôn là HAS_PRICE
      listPrice: price.listPrice || '',
      salePrice: price.salePrice || '',
      currency: price.currency || 'VND',
      description: product.description || '',
      tags: product.tags || [],
      customFields: [],
      media: [],
      shipmentConfig: undefined,
      // Combo product defaults
      comboProducts: comboItems.map((item: Record<string, unknown>) => ({
        productId: item['productId'] as number,
        productName: (item['productName'] as string) || `Product ${item['productId']}`,
        quantity: (item['total'] as number) || 1,
        discountPercent: 0,
      })),
    };
  }, [product]);

  // Sync tags với state khi product thay đổi
  useEffect(() => {
    if (product?.tags) {
      setTempTags(product.tags);
    }
  }, [product?.tags]);

  // Sync existing images với state khi product thay đổi
  useEffect(() => {
    if (product?.images && product.images.length > 0) {
      // Convert existing images to ExtendedFileWithMetadata format for display
      const existingImages: ExtendedFileWithMetadata[] = product.images.map((img, index) => {
        // Extract file name from URL or use a default name
        const urlParts = img.url.split('/');
        const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `image-${index + 1}`;

        return {
          id: `existing-${img.key}`,
          file: new File([], fileName, { type: 'image/jpeg' }), // Placeholder file with proper name
          preview: img.url, // This is the key - set preview to the image URL for display
          // Thêm metadata để track key và url cho việc xóa
          metadata: {
            key: img.key,
            url: img.url,
            isExisting: true,
          },
        };
      });

      // Chỉ set lại mediaFiles nếu chưa có ảnh nào hoặc số lượng ảnh khác nhau
      // Điều này tránh ghi đè ảnh mới đã được thêm vào
      setMediaFiles(prev => {
        // Nếu chưa có ảnh existing nào, set toàn bộ
        const currentExistingCount = prev.filter(file => file.id.startsWith('existing-')).length;
        if (currentExistingCount === 0) {
          return existingImages;
        }

        // Nếu số lượng ảnh từ API khác với số ảnh existing hiện tại, cập nhật
        if (existingImages.length !== currentExistingCount) {
          // Giữ lại ảnh mới (không phải existing) và thêm ảnh existing mới
          const newFiles = prev.filter(file => !file.id.startsWith('existing-'));
          const result = [...existingImages, ...newFiles];
          return result;
        }

        // Nếu số lượng giống nhau, chỉ cập nhật URL cho ảnh existing (có thể URL đã thay đổi)
        const result = prev.map(file => {
          if (file.id.startsWith('existing-')) {
            const matchingImage = existingImages.find(img => img.id === file.id);
            if (matchingImage) {
              return {
                ...file,
                preview: matchingImage.preview || file.preview || '', // Đảm bảo preview không undefined
                metadata: matchingImage.metadata || file.metadata || {
                  key: '',
                  url: '',
                  isExisting: true,
                }, // Đảm bảo metadata không undefined
              };
            }
          }
          return file;
        });
        return result;
      });
    } else {
      // Nếu không có ảnh từ API, chỉ giữ lại ảnh mới
      setMediaFiles(prev => prev.filter(file => !file.id.startsWith('existing-')));
    }
  }, [product?.images]);

  // Sync combo products với state khi product thay đổi
  useEffect(() => {
    if (product?.advancedInfo?.info && Array.isArray(product.advancedInfo.info)) {
      const comboItems = product.advancedInfo.info.map((item: Record<string, unknown>) => ({
        productId: item['productId'] as number,
        productName: `Product ${item['productId']}`, // Tạm thời, sẽ được cập nhật khi load xong
        quantity: (item['total'] as number) || 1,
        discountPercent: 0,
      }));
      setComboProducts(comboItems);
    }
  }, [product?.advancedInfo?.info]);

  // Load thông tin chi tiết cho các sản phẩm trong combo
  useEffect(() => {
    const loadComboProductDetails = async () => {
      if (comboProducts.length === 0) return;
      try {
        // Load thông tin cho các sản phẩm chưa có trong cache
        const productIdsToLoad = comboProducts
          .map(item => item.productId.toString())
          .filter(id => !loadedProducts.has(id));

        if (productIdsToLoad.length > 0) {
          // Load từng sản phẩm
          const loadPromises = productIdsToLoad.map(async (productId) => {
            try {
              const productData = await ProductService.getProductById(Number(productId));
              return { id: productId, data: productData };
            } catch (error) {
              console.error(`❌ [ComboProductEditForm] Error loading product ${productId}:`, error);
              return null;
            }
          });

          const loadResults = await Promise.all(loadPromises);

          // Cập nhật cache với sản phẩm đã load thành công
          const newLoadedProducts = new Map(loadedProducts);
          loadResults.forEach(result => {
            if (result) {
              newLoadedProducts.set(result.id, result.data);
            }
          });
          setLoadedProducts(newLoadedProducts);

          // Cập nhật tên sản phẩm trong combo
          setComboProducts(prev => prev.map(item => {
            const productData = newLoadedProducts.get(item.productId.toString());
            if (productData) {
              return {
                ...item,
                productName: productData.name || `Product ${item.productId}`,
              };
            }
            return item;
          }));

        } else {
          // Tất cả sản phẩm đã có trong cache, chỉ cần cập nhật tên
          setComboProducts(prev => prev.map(item => {
            const productData = loadedProducts.get(item.productId.toString());
            if (productData) {
              return {
                ...item,
                productName: productData.name || `Product ${item.productId}`,
              };
            }
            return item;
          }));
        }
      } catch (error) {
        console.error('❌ [ComboProductEditForm] Error loading combo product details:', error);
      }
    };

    loadComboProductDetails();
  }, [comboProducts, loadedProducts]); // Chạy khi combo products hoặc loaded products thay đổi

  // Sync existing custom fields với state khi product thay đổi
  useEffect(() => {
    if (product?.metadata?.customFields && product.metadata.customFields.length > 0) {
      const existingCustomFields: SelectedCustomField[] = product.metadata.customFields.map((field) => ({
        id: field.id,
        fieldId: field.id, // Sử dụng id làm fieldId
        label: field.label,
        component: field.type,
        type: field.type,
        required: field.required,
        configJson: field.configJson,
        value: field.value,
      }));
      setProductCustomFields(existingCustomFields);
    }
  }, [product?.metadata?.customFields]);

  // Sync form values với product data khi product thay đổi (chỉ chạy khi product thay đổi)
  useEffect(() => {
    if (product && formRef.current) {
      const price = product.price as HasPriceDto;

      // Delay để đảm bảo Form component đã mount hoàn toàn
      const timer = setTimeout(() => {
        if (formRef.current) {
          // Set form values - chỉ set khi product thay đổi, không ghi đè user input
          formRef.current.setValues({
            name: product.name || '',
            typePrice: PriceTypeEnum.HAS_PRICE,
            listPrice: price?.listPrice || '',
            salePrice: price?.salePrice || '',
            currency: price?.currency || 'VND',
            description: product.description || '',
            tags: product.tags || [],
            // Shipment config từ response
            shipmentConfig: product.shipmentConfig || {
              widthCm: '',
              heightCm: '',
              lengthCm: '',
              weightGram: '',
            },
            comboProducts: [], // Sẽ được update riêng trong useEffect khác
          });
        }
      }, 100); // Delay 100ms

      return () => clearTimeout(timer);
    }
    // Return undefined explicitly for cases where the effect doesn't need cleanup
    return undefined;
  }, [product]); // Chỉ dependency vào product, không phụ thuộc vào comboProducts

  // Handler để xử lý khi ảnh bị xóa
  const handleMediaFilesChange = useCallback((files: ExtendedFileWithMetadata[]) => {
    // Tìm những ảnh existing đã bị xóa
    const currentExistingFiles = mediaFiles.filter(file => file.id.startsWith('existing-'));
    const newExistingFiles = files.filter(file => file.id.startsWith('existing-'));

    const removedFiles = currentExistingFiles.filter(
      currentFile => !newExistingFiles.find(newFile => newFile.id === currentFile.id)
    );

    // Thêm vào danh sách ảnh bị xóa
    if (removedFiles.length > 0) {
      const removedImageInfo = removedFiles
        .filter(file => file.metadata?.key && file.metadata?.url)
        .map(file => ({
          key: file.metadata!.key as string,
          url: file.metadata!.url as string,
        }));

      setDeletedImages(prev => [...prev, ...removedImageInfo]);
    }

    setMediaFiles(files);
  }, [mediaFiles]);

  // Hàm lấy giá sản phẩm
  const getProductPrice = useCallback((product: ProductDto): number => {
    if (product.typePrice === PriceTypeEnum.HAS_PRICE && product.price) {
      const price = product.price as HasPriceDto;
      return price.salePrice || price.listPrice || 0;
    }
    return 0;
  }, []);

  // Hàm tính tổng giá niêm yết của combo
  const calculateComboListPrice = useCallback((): number => {
    return comboProducts.reduce((total, item) => {
      const productData = loadedProducts.get(item.productId.toString());
      const originalPrice = productData ? getProductPrice(productData) : 0;
      return total + (originalPrice * item.quantity);
    }, 0);
  }, [comboProducts, loadedProducts, getProductPrice]);

  // Load sản phẩm cho AsyncSelectWithPagination
  const loadProducts = useCallback(async ({ search, page, limit }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await ProductService.getProducts({
        search: search || '',
        page: page || 1,
        limit: limit || 20,
      });

      // Cache products
      const newProducts = new Map(loadedProducts);
      response.items.forEach((product: ProductDto) => {
        newProducts.set(product.id.toString(), product);
      });
      setLoadedProducts(newProducts);

      return {
        items: response.items.map((product: ProductDto) => ({
          value: product.id.toString(),
          label: product.name || 'N/A',
          subtitle: `${getProductPrice(product).toLocaleString('vi-VN')} VND`,
          data: product as unknown as Record<string, unknown>,
        })),
        totalItems: response.meta.totalItems,
        totalPages: response.meta.totalPages,
        currentPage: response.meta.currentPage,
      };
    } catch {
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  }, [loadedProducts, getProductPrice]);

  // Thêm sản phẩm vào combo
  const handleAddComboProduct = useCallback((productId: number, productName: string) => {
    const existingProduct = comboProducts.find(item => item.productId === productId);
    if (existingProduct) {
      NotificationUtil.warning({
        message: t('business:product.form.comboProduct.productAlreadyExists'),
        duration: 3000,
      });
      return;
    }

    const newComboProduct: ComboProductItem = {
      productId,
      productName,
      quantity: 1,
      discountPercent: 0,
    };

    setComboProducts(prev => [...prev, newComboProduct]);
  }, [comboProducts, t]);

  // Xử lý chọn sản phẩm từ AsyncSelectWithPagination
  const handleProductSelect = useCallback((value: string | number | string[] | number[] | undefined) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return;
    }

    // Get the first value if it's an array
    const productId = Array.isArray(value) ? value[0] : value;
    if (!productId) return;
    const productIdStr = productId.toString();

    // Get product data from loaded products
    const productData = loadedProducts.get(productIdStr);

    if (productData) {
      handleAddComboProduct(Number(productData.id), productData.name);
    }
  }, [loadedProducts, handleAddComboProduct]);

  // Xóa sản phẩm khỏi combo
  const handleRemoveComboProduct = useCallback((productId: number) => {
    setComboProducts(prev => prev.filter(item => item.productId !== productId));
  }, []);

  // Cập nhật số lượng sản phẩm trong combo
  const handleUpdateComboProductQuantity = useCallback((productId: number, quantity: number) => {
    if (quantity < 1) return;
    setComboProducts(prev =>
      prev.map(item =>
        item.productId === productId ? { ...item, quantity } : item
      )
    );
  }, []);

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Đồng bộ comboProducts với form khi state thay đổi
  useEffect(() => {
    if (formRef.current && comboProducts.length > 0) {
      console.log('🔄 [ComboProductEditForm] Updating comboProducts in form:', comboProducts);

      // Chỉ update comboProducts field, không ghi đè các field khác
      const currentValues = formRef.current.getValues();
      console.log('📋 [ComboProductEditForm] Current form values before update:', currentValues);

      formRef.current.setValues({
        ...currentValues,
        comboProducts,
      });

      console.log('✅ [ComboProductEditForm] ComboProducts updated in form');
    }
  }, [comboProducts]);

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    if (!values['name']) {
      console.error('❌ Missing required fields:', {
        name: values['name'],
      });
      NotificationUtil.error({
        message: t('business:product.form.validation.comboRequiredFieldsMissing'),
        duration: 3000,
      });
      return;
    }

    if (comboProducts.length === 0) {
      NotificationUtil.error({
        message: t('business:product.form.validation.comboProductsRequired'),
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ComboProductFormValues;
      setIsUploading(true);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : t('business:product.form.validation.priceValidationError'),
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Chỉ gửi những trường cần thiết cho combo sản phẩm
      const productData: Omit<UpdateProductDto, 'advancedInfo'> & { advancedInfo?: Record<string, unknown> } = {
        name: formValues.name,
        typePrice: PriceTypeEnum.HAS_PRICE,
        price: priceData,
      };

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (formValues.tags && formValues.tags.length > 0) {
        productData.tags = formValues.tags;
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
          const fieldValue = field.value?.['value'];

          // Nếu là undefined hoặc null thì loại bỏ
          if (fieldValue === undefined || fieldValue === null) {
            return false;
          }

          // Nếu là string rỗng thì loại bỏ
          if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
            return false;
          }

          // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
          return true;
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Tạo combo advanced info theo API structure
      const advancedInfo: Record<string, unknown> = {
        purchaseCount: 0,
        info: comboProducts.map(item => ({
          productId: item.productId,
          total: item.quantity,
        })),
      };

      productData.advancedInfo = advancedInfo;

      // Xử lý ảnh theo format yêu cầu: DELETE existing images + ADD new images
      const imageOperations: Array<{
        operation: 'DELETE' | 'ADD';
        key?: string;
        index?: number;
        mimeType?: string;
      }> = [];

      // Thêm operations DELETE cho ảnh bị xóa
      if (deletedImages.length > 0) {
        deletedImages.forEach(deletedImage => {
          imageOperations.push({
            operation: 'DELETE',
            key: deletedImage.key,
          });
        });
      }

      // Thêm operations ADD cho ảnh mới
      const newMediaFiles = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      if (newMediaFiles.length > 0) {
        newMediaFiles.forEach((file, index) => {
          imageOperations.push({
            operation: 'ADD',
            index: index,
            mimeType: file.file.type,
          });
        });
      }

      // Chỉ thêm images vào request nếu có operations
      if (imageOperations.length > 0) {
        productData.images = imageOperations;
      }

      // Log request data để debug
      console.log('🔍 [ComboProductEditForm] Request data:', {
        productId,
        productData,
        imageOperations,
        deletedImages,
        newMediaFiles: newMediaFiles.length,
      });

      // Gọi API cập nhật sản phẩm
      const response = await updateProductMutation.mutateAsync({
        id: productId,
        data: productData as unknown as UpdateProductDto,
      });

      console.log('✅ [ComboProductEditForm] Update response received:', response);

      // Upload media nếu có và API trả về images với upload URLs
      // Only upload new images (not existing ones that start with "existing-")
      const newMediaFilesForUpload = mediaFiles.filter(file => !file.id.startsWith('existing-'));

      if (newMediaFilesForUpload.length > 0) {
        try {
          console.log('🔍 [ComboProductEditForm] Response structure:', response);
          console.log('🔍 [ComboProductEditForm] Response type:', typeof response);
          console.log('🔍 [ComboProductEditForm] Response keys:', response ? Object.keys(response) : 'null');

          // Kiểm tra nhiều format response khác nhau
          let uploadUrls: Array<{ url: string; key: string; index: number }> = [];
          let hasValidUploadUrls = false;

          // Format 1: response.uploadUrls.imagesUploadUrls (business module format)
          if (response &&
              typeof response === 'object' &&
              'uploadUrls' in response &&
              response.uploadUrls &&
              typeof response.uploadUrls === 'object' &&
              'imagesUploadUrls' in response.uploadUrls &&
              Array.isArray(response.uploadUrls.imagesUploadUrls)) {
            uploadUrls = response.uploadUrls.imagesUploadUrls;
            hasValidUploadUrls = true;
            console.log('✅ [ComboProductEditForm] Found uploadUrls.imagesUploadUrls format');
          }
          // Format 2: response.presignedUrlImage (admin marketplace format)
          else if (response &&
                   typeof response === 'object' &&
                   'presignedUrlImage' in response &&
                   Array.isArray(response.presignedUrlImage)) {
            uploadUrls = response.presignedUrlImage
              .map((item: UploadUrlItem, index: number) => {
                const url = item.uploadUrl || item.url;
                if (!url) return null; // Filter out items without valid URLs
                return {
                  url,
                  key: item.key || `image-${index}`,
                  index: item.index !== undefined ? item.index : index,
                };
              })
              .filter((item): item is { url: string; key: string; index: number } => item !== null);
            hasValidUploadUrls = true;
            console.log('✅ [ComboProductEditForm] Found presignedUrlImage format');
          }
          // Format 3: response.result.presignedUrlImage (wrapped format)
          else if (response &&
                   typeof response === 'object' &&
                   'result' in response &&
                   response.result &&
                   typeof response.result === 'object' &&
                   'presignedUrlImage' in response.result &&
                   Array.isArray(response.result.presignedUrlImage)) {
            uploadUrls = response.result.presignedUrlImage
              .map((item: UploadUrlItem, index: number) => {
                const url = item.uploadUrl || item.url;
                if (!url) return null; // Filter out items without valid URLs
                return {
                  url,
                  key: item.key || `image-${index}`,
                  index: item.index !== undefined ? item.index : index,
                };
              })
              .filter((item): item is { url: string; key: string; index: number } => item !== null);
            hasValidUploadUrls = true;
            console.log('✅ [ComboProductEditForm] Found result.presignedUrlImage format');
          }

          console.log('🔍 [ComboProductEditForm] Upload URLs found:', uploadUrls);
          console.log('🔍 [ComboProductEditForm] Has valid upload URLs:', hasValidUploadUrls);

          if (hasValidUploadUrls && uploadUrls.length > 0) {
            // Tạo mapping giữa new media files và upload URLs từ backend
            const uploadTasks = newMediaFilesForUpload.slice(0, uploadUrls.length).map((fileData, index) => {
              const uploadInfo = uploadUrls[index];
              if (!uploadInfo) {
                throw new Error(`Upload info not found for index ${index}`);
              }
              return {
                file: fileData.file,
                uploadUrl: uploadInfo.url,
                key: uploadInfo.key,
                index: uploadInfo.index,
              };
            });

            console.log('🔍 [ComboProductEditForm] Upload tasks:', uploadTasks);

            // Tạo array các file và URLs để upload cùng lúc
            const filesToUpload = uploadTasks.map((task, index) => ({
              file: task.file,
              id: `${Date.now()}_${index}`,
            }));
            const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

            console.log('🔍 [ComboProductEditForm] Files to upload:', filesToUpload.length);
            console.log('🔍 [ComboProductEditForm] URLs to upload:', urlsToUpload.length);

            // Upload tất cả ảnh mới cùng lúc, skip cache invalidation trong hook
            // Cache invalidation đã được xử lý trong useUpdateProduct hook
            await uploadProductImages(filesToUpload, urlsToUpload, {
              skipCacheInvalidation: true,
            });

            console.log('✅ [ComboProductEditForm] Images uploaded successfully');

            NotificationUtil.success({
              message: t(
                'business:product.mediaUploadSuccess',
                'Tải lên ảnh sản phẩm thành công'
              ),
              duration: 3000,
            });
          } else {
            console.log('⚠️ [ComboProductEditForm] No valid upload URLs found');
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ [ComboProductEditForm] Error uploading images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      // Reset deletedImages sau khi update thành công
      setDeletedImages([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.updateSuccess', 'Cập nhật sản phẩm thành công'),
        duration: 3000,
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      setIsUploading(false);

      console.error('❌ [ComboProductEditForm] Error in handleSubmit:', error);
      console.error('❌ [ComboProductEditForm] Error type:', typeof error);
      console.error('❌ [ComboProductEditForm] Error keys:', error ? Object.keys(error) : 'null');

      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        console.error('❌ [ComboProductEditForm] Validation error:', error);
        NotificationUtil.error({
          message: t('business:product.form.validation.formValidationError', 'Lỗi validation dữ liệu'),
          duration: 3000,
        });
      } else {
        console.error('❌ [ComboProductEditForm] General error:', error);
        NotificationUtil.error({
          message: t('business:product.updateError', 'Có lỗi xảy ra khi cập nhật sản phẩm'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá cho combo (luôn là HAS_PRICE)
  const getPriceData = useCallback((values: ComboProductFormValues): HasPriceDto => {
    if (!values.salePrice || values.salePrice === '') {
      throw new Error(t('business:product.form.validation.salePriceRequired'));
    }
    if (!values.currency || values.currency.trim() === '') {
      throw new Error(t('business:product.form.validation.currencyRequired'));
    }

    const salePrice = Number(values.salePrice);
    if (isNaN(salePrice) || salePrice <= 0) {
      throw new Error(t('business:product.form.validation.salePriceInvalid'));
    }

    // Tính giá niêm yết từ tổng giá sản phẩm trong combo
    const listPrice = calculateComboListPrice();
    if (listPrice <= 0) {
      throw new Error(t('business:product.form.validation.comboListPriceInvalid'));
    }

    // Kiểm tra giá niêm yết phải lớn hơn giá bán
    if (listPrice <= salePrice) {
      throw new Error(t('business:product.form.validation.listPriceGreaterThanSale'));
    }

    return {
      listPrice,
      salePrice,
      currency: values.currency.trim(),
    };
  }, [calculateComboListPrice, t]);

  // Hiển thị loading khi đang fetch chi tiết sản phẩm
  if (isLoadingProduct) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Typography variant="body1">
            {t('business:product.form.loadingProduct', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </FormMultiWrapper>
    );
  }

  // Hiển thị lỗi nếu không tải được sản phẩm
  if (productError || !product) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-red-500">
            {t('business:product.form.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </FormMultiWrapper>
    );
  }

  return (
    <FormMultiWrapper title={t('business:product.form.editComboTitle', 'Chỉnh sửa combo sản phẩm')}>
      <Form
        ref={formRef}
        schema={comboProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || t('business:product.form.validation.formValidationError');
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.comboNamePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.comboDescriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Sản phẩm trong combo */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.comboProducts', '2. Sản phẩm trong combo')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Tìm kiếm và chọn sản phẩm */}
            <div className="space-y-3">
              <Typography variant="body2" className="font-medium">
                {t('business:product.form.comboProduct.searchProductLabel')}
              </Typography>
              <AsyncSelectWithPagination
                loadOptions={loadProducts}
                placeholder={t('business:order.searchProductPlaceholder', 'Tìm kiếm sản phẩm...')}
                searchOnEnter={true}
                debounceTime={300}
                itemsPerPage={20}
                noOptionsMessage={t('business:order.noProductsFound', 'Không tìm thấy sản phẩm')}
                loadingMessage={t('common:loading', 'Đang tải...')}
                onChange={handleProductSelect}
                fullWidth
              />
            </div>

            {/* Danh sách sản phẩm đã chọn */}
            {comboProducts.length > 0 && (
              <div className="space-y-4">
                <Typography variant="subtitle2" className="font-medium text-muted-foreground">
                  {t('business:product.form.comboProduct.selectedProductsLabel')} ({comboProducts.length}):
                </Typography>

                {/* Header */}
                <div className="grid grid-cols-10 gap-4 py-3 px-4 bg-gray-50 dark:bg-gray-800 rounded-lg font-medium text-sm">
                  <div className="col-span-5 flex items-center">{t('business:product.form.comboProduct.tableHeaders.productName')}</div>
                  <div className="col-span-2 text-center flex items-center justify-center">{t('business:product.form.comboProduct.tableHeaders.quantity')}</div>
                  <div className="col-span-2 text-center flex items-center justify-center">{t('business:product.form.comboProduct.tableHeaders.originalPrice')}</div>
                  <div className="col-span-1 text-center flex items-center justify-center">{t('business:product.form.comboProduct.tableHeaders.actions')}</div>
                </div>

                {/* Product rows */}
                <div className="space-y-2">
                  {comboProducts.map(item => {
                    const productData = loadedProducts.get(item.productId.toString());
                    const originalPrice = productData ? getProductPrice(productData) : 0;

                    return (
                      <div key={item.productId} className="grid grid-cols-10 gap-4 py-3 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg items-center">
                        {/* Tên sản phẩm */}
                        <div className="col-span-5">
                          <div className="flex items-center gap-3">
                            {productData?.images?.[0]?.url && (
                              <img
                                src={productData.images[0].url}
                                alt={item.productName}
                                className="w-10 h-10 object-cover rounded"
                              />
                            )}
                            <Typography variant="subtitle2" className="font-medium">
                              {item.productName}
                            </Typography>
                          </div>
                        </div>

                        {/* Số lượng */}
                        <div className="col-span-2 flex justify-center items-center">
                          <div className="flex items-center gap-1">
                            <IconCard
                              icon="minus"
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUpdateComboProductQuantity(
                                item.productId,
                                Math.max(1, item.quantity - 1)
                              )}
                              className="cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
                            />
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={e => handleUpdateComboProductQuantity(
                                item.productId,
                                parseInt(e.target.value) || 1
                              )}
                              className="w-16 text-center"
                            />
                            <IconCard
                              icon="plus"
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUpdateComboProductQuantity(
                                item.productId,
                                item.quantity + 1
                              )}
                              className="cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
                            />
                          </div>
                        </div>

                        {/* Giá gốc */}
                        <div className="col-span-2 text-center flex items-center justify-center">
                          <Typography variant="body2">
                            {originalPrice.toLocaleString('vi-VN')}đ
                          </Typography>
                        </div>

                        {/* Thao tác */}
                        <div className="col-span-1 flex justify-center items-center">
                          <IconCard
                            icon="trash"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveComboProduct(item.productId)}
                            className="cursor-pointer text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Tổng cộng */}
                <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-right">
                    <Typography variant="subtitle1" className="font-semibold">
                      {t('business:product.form.comboProduct.totalLabel')} {comboProducts.reduce((total, item) => {
                        const productData = loadedProducts.get(item.productId.toString());
                        const originalPrice = productData ? getProductPrice(productData) : 0;
                        return total + (originalPrice * item.quantity);
                      }, 0).toLocaleString('vi-VN')}đ
                    </Typography>
                  </div>
                </div>
              </div>
            )}

            {comboProducts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Typography variant="body2">
                  {t('business:product.form.comboProduct.noProductsMessage')}
                </Typography>
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* 3. Giá combo */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '3. Giá combo')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-foreground">
                    {t('business:product.listPrice', 'Giá niêm yết')}
                  </label>
                  <Input
                    fullWidth
                    disabled
                    value={`${calculateComboListPrice().toLocaleString('vi-VN')}đ`}
                    readOnly
                  />
                  <Typography variant="caption" className="text-muted-foreground">
                    {t('business:product.form.comboProduct.listPriceNote', 'Được tính tự động từ tổng giá sản phẩm trong combo')}
                  </Typography>
                </div>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.form.salePricePlaceholder')} />
                </FormItem>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Input
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={e => field.onChange(e.target.value)}
                        placeholder="VND"
                        disabled
                        readOnly
                      />
                    )}
                  />
                </FormItem>
                <div className="flex items-end">
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('business:product.form.comboProduct.salePriceNote', 'Giá bán phải nhỏ hơn giá niêm yết')}
                  </Typography>
                </div>
              </div>
            </div>

            {/* Hidden field để form validation */}
            <div className="hidden">
              <FormItem name="typePrice">
                <Input value={PriceTypeEnum.HAS_PRICE} readOnly />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* 4. Hình ảnh combo */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '4. Hình ảnh combo')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: ExtendedFileWithMetadata[]) => {
                      handleMediaFilesChange(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>



        {/* 6. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '6. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => {
                  const fieldValue = field.value?.['value'] as string | number | boolean || '';

                  // Debug logging
                  console.log(`🔍 ComboProductEdit CustomField ${field.label}:`, {
                    fieldId: field.fieldId,
                    type: field.type,
                    component: field.component,
                    rawValue: field.value,
                    extractedValue: fieldValue,
                    valueType: typeof fieldValue
                  });

                  return (
                    <CustomFieldRenderer
                      key={field.id}
                      field={field}
                      value={fieldValue}
                      onChange={(value: string | number | boolean) => {
                        handleUpdateCustomFieldInProduct(field.id, value);
                      }}
                      onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="save"
            title={
              updateProductMutation.isPending || isUploading
                ? t('business:product.form.updating', 'Đang cập nhật...')
                : t('business:product.form.update', 'Cập nhật combo')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={updateProductMutation.isPending || isUploading || comboProducts.length === 0}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ComboProductEditForm;
