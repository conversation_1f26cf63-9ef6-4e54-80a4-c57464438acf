import { z } from 'zod';
import { AFFILIATE_CONSTANTS } from '../constants';

/**
 * Schema validation cho withdrawal request
 */
export const withdrawalRequestSchema = z.object({
  amount: z
    .number()
    .min(AFFILIATE_CONSTANTS.MIN_WITHDRAWAL_AMOUNT, 'Số tiền rút tối thiểu là 100,000 VND')
    .max(AFFILIATE_CONSTANTS.MAX_WITHDRAWAL_AMOUNT, 'Số tiền rút tối đa là 50,000,000 VND'),
});

/**
 * Schema validation cho convert points
 */
export const convertPointsSchema = z.object({
  points: z
    .number()
    .min(AFFILIATE_CONSTANTS.MIN_POINTS_CONVERSION, 'Số point tối thiểu để đổi là 100')
    .int('Số point phải là số nguyên'),
});

export type WithdrawalRequestData = z.infer<typeof withdrawalRequestSchema>;
export type ConvertPointsData = z.infer<typeof convertPointsSchema>;