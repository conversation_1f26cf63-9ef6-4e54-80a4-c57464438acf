import { useState, useEffect, useCallback } from 'react';
import { OrderItemDto } from '../types/order.types';
import { ComboShippingService } from '../services/combo-shipping.service';

/**
 * Interface cho kết quả kiểm tra shipping
 */
export interface ShippingCheckResult {
  needsShipping: boolean;
  isLoading: boolean;
  error: string | null;
  physicalProductsCount: number;
}

/**
 * Hook để kiểm tra order items có cần vận chuyển không
 * Xử lý cả sản phẩm đơn lẻ và combo
 */
export const useComboShipping = (orderItems: OrderItemDto[]) => {
  const [result, setResult] = useState<ShippingCheckResult>({
    needsShipping: false,
    isLoading: false,
    error: null,
    physicalProductsCount: 0,
  });

  // Kiểm tra shipping needs
  const checkShippingNeeds = useCallback(async () => {
    if (!orderItems || orderItems.length === 0) {
      setResult({
        needsShipping: false,
        isLoading: false,
        error: null,
        physicalProductsCount: 0,
      });
      return;
    }

    setResult(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('🔍 [useComboShipping] Checking shipping needs for order items:', orderItems);

      // Kiểm tra có cần vận chuyển không
      const needsShipping = await ComboShippingService.checkOrderItemsNeedShipping(orderItems);

      // Lấy danh sách sản phẩm vật lý để đếm
      const physicalProducts = await ComboShippingService.getPhysicalProductsForShipping(orderItems);

      setResult({
        needsShipping,
        isLoading: false,
        error: null,
        physicalProductsCount: physicalProducts.length,
      });

      console.log('✅ [useComboShipping] Shipping check completed:', {
        needsShipping,
        physicalProductsCount: physicalProducts.length,
      });
    } catch (error) {
      console.error('❌ [useComboShipping] Error checking shipping needs:', error);
      
      setResult({
        needsShipping: true, // Fallback: giả sử cần vận chuyển
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        physicalProductsCount: 0,
      });
    }
  }, [orderItems]);

  // Auto-check khi orderItems thay đổi
  useEffect(() => {
    checkShippingNeeds();
  }, [checkShippingNeeds]);

  // Retry function
  const retry = useCallback(() => {
    checkShippingNeeds();
  }, [checkShippingNeeds]);

  return {
    ...result,
    retry,
  };
};

/**
 * Hook để lấy danh sách sản phẩm vật lý từ order items
 * Dùng để tính phí vận chuyển
 */
export const usePhysicalProductsForShipping = (orderItems: OrderItemDto[]) => {
  const [physicalProducts, setPhysicalProducts] = useState<Array<{
    productId: number;
    quantity: number;
    shipmentConfig?: {
      lengthCm?: number;
      widthCm?: number;
      heightCm?: number;
      weightGram?: number;
    };
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getPhysicalProducts = useCallback(async () => {
    if (!orderItems || orderItems.length === 0) {
      setPhysicalProducts([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('📦 [usePhysicalProductsForShipping] Getting physical products from order items:', orderItems);

      const products = await ComboShippingService.getPhysicalProductsForShipping(orderItems);
      setPhysicalProducts(products);

      console.log('✅ [usePhysicalProductsForShipping] Got physical products:', products);
    } catch (err) {
      console.error('❌ [usePhysicalProductsForShipping] Error getting physical products:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // Fallback: treat all items as physical
      setPhysicalProducts(orderItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        ...(item.shipmentConfig && { shipmentConfig: item.shipmentConfig }),
      })));
    } finally {
      setIsLoading(false);
    }
  }, [orderItems]);

  useEffect(() => {
    getPhysicalProducts();
  }, [getPhysicalProducts]);

  return {
    physicalProducts,
    isLoading,
    error,
    retry: getPhysicalProducts,
  };
};

/**
 * Hook đơn giản để kiểm tra một combo có chứa sản phẩm vật lý không
 */
export const useComboPhysicalCheck = (comboProductId: number | null) => {
  const [hasPhysicalProducts, setHasPhysicalProducts] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkCombo = useCallback(async () => {
    if (!comboProductId) {
      setHasPhysicalProducts(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const hasPhysical = await ComboShippingService.checkComboContainsPhysicalProducts(comboProductId);
      setHasPhysicalProducts(hasPhysical);
    } catch (err) {
      console.error('❌ [useComboPhysicalCheck] Error checking combo:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setHasPhysicalProducts(true); // Fallback
    } finally {
      setIsLoading(false);
    }
  }, [comboProductId]);

  useEffect(() => {
    checkCombo();
  }, [checkCombo]);

  return {
    hasPhysicalProducts,
    isLoading,
    error,
    retry: checkCombo,
  };
};
