import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  CreateWarehouseDto,
  UpdateWarehouseDto,
  WarehouseDetailDto,
  WarehouseDto,
  WarehouseListItemDto,
  WarehouseQueryParams,
  PhysicalWarehouseDto,
  QueryPhysicalWarehouseDto,
  PhysicalWarehouseListResponse,
  WarehouseLoadOptionsResponse,
  CreatePhysicalWarehouseDto,
  CreatePhysicalWarehouseWithDetailsDto,
  UpdatePhysicalWarehouseDto,
} from '../types/warehouse.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { SelectOption } from '@/shared/components/common/Select/Select';

/**
 * Service xử lý API liên quan đến kho
 */
export const WarehouseService = {
  /**
   * L<PERSON>y danh sách kho
   * @param params Tham số truy vấn
   * @returns Danh sách kho với phân trang
   */
  getWarehouses: async (params?: WarehouseQueryParams): Promise<ApiResponseDto<PaginatedResult<WarehouseListItemDto>>> => {
    return apiRequest.get('/user/warehouses', { params });
  },

  /**
   * Lấy chi tiết kho theo ID
   * @param id ID của kho
   * @returns Chi tiết kho
   */
  getWarehouseById: async (id: number): Promise<ApiResponseDto<WarehouseDetailDto>> => {
    return apiRequest.get(`/user/warehouses/${id}`);
  },

  /**
   * Tạo kho mới
   * @param data Dữ liệu tạo kho
   * @returns Thông tin kho đã tạo
   */
  createWarehouse: async (data: CreateWarehouseDto): Promise<ApiResponseDto<WarehouseDto>> => {
    return apiRequest.post('/user/warehouses', data);
  },

  /**
   * Cập nhật kho
   * @param id ID của kho
   * @param data Dữ liệu cập nhật kho
   * @returns Thông tin kho đã cập nhật
   */
  updateWarehouse: async (id: number, data: UpdateWarehouseDto): Promise<ApiResponseDto<WarehouseDto>> => {
    return apiRequest.put(`/user/warehouses/${id}`, data);
  },

  /**
   * Xóa kho
   * @param id ID của kho
   * @returns Thông báo xóa thành công
   */
  deleteWarehouse: async (id: number): Promise<ApiResponseDto<null>> => {
    return apiRequest.delete(`/user/warehouses/${id}`);
  },

  // ===== PHYSICAL WAREHOUSE METHODS =====

  /**
   * Lấy danh sách kho vật lý với logic nghiệp vụ
   */
  getPhysicalWarehouses: async (
    params?: QueryPhysicalWarehouseDto
  ): Promise<PhysicalWarehouseListResponse> => {
    // Thiết lập tham số mặc định
    const defaultParams: QueryPhysicalWarehouseDto = {
      page: 1,
      limit: 20,
      sortBy: 'warehouseId',
      sortDirection: SortDirection.ASC ,
      ...params,
    };

    // Validate limit không vượt quá 100
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit không thể vượt quá 100');
    }

    const response: ApiResponseDto<PhysicalWarehouseListResponse> = await apiRequest.get(
      '/user/physical-warehouses',
      { params: defaultParams }
    );

    if (response.code !== 200) {
      throw new Error(response.message || 'Lỗi khi lấy danh sách kho vật lý');
    }

    return response.result;
  },

  /**
   * Lấy thông tin kho vật lý theo ID với xử lý lỗi
   */
  getPhysicalWarehouseById: async (warehouseId: number): Promise<PhysicalWarehouseDto> => {
    if (!warehouseId || warehouseId <= 0) {
      throw new Error('ID kho không hợp lệ');
    }

    const response: ApiResponseDto<PhysicalWarehouseDto> = await apiRequest.get(
      `/user/physical-warehouses/${warehouseId}`
    );

    if (response.code !== 200) {
      throw new Error(response.message || 'Lỗi khi lấy thông tin kho vật lý');
    }

    return response.result;
  },

  /**
   * Tạo mới kho vật lý với validation
   */
  createPhysicalWarehouse: async (
    data: CreatePhysicalWarehouseDto
  ): Promise<PhysicalWarehouseDto> => {
    // Validate dữ liệu đầu vào
    if (!data.name?.trim()) {
      throw new Error('Tên kho không được để trống');
    }

    if (!data.address?.trim()) {
      throw new Error('Địa chỉ kho không được để trống');
    }

    if (data.capacity !== undefined && data.capacity < 0) {
      throw new Error('Sức chứa kho phải >= 0');
    }

    const response: ApiResponseDto<PhysicalWarehouseDto> = await apiRequest.post(
      '/user/physical-warehouses',
      data
    );

    if (response.code !== 200 && response.code !== 201) {
      throw new Error(response.message || 'Lỗi khi tạo kho vật lý');
    }

    return response.result;
  },

  /**
   * Tạo mới kho vật lý với chi tiết (API mới)
   */
  createPhysicalWarehouseWithDetails: async (
    data: CreatePhysicalWarehouseWithDetailsDto
  ): Promise<PhysicalWarehouseDto> => {
    // Validate dữ liệu đầu vào
    if (!data.name?.trim()) {
      throw new Error('Tên kho không được để trống');
    }

    if (!data.address?.trim()) {
      throw new Error('Địa chỉ kho không được để trống');
    }

    if (data.capacity !== undefined && data.capacity < 0) {
      throw new Error('Sức chứa kho phải >= 0');
    }

    const response: ApiResponseDto<PhysicalWarehouseDto> = await apiRequest.post(
      '/user/physical-warehouses/create/new',
      data
    );

    if (response.code !== 200 && response.code !== 201) {
      throw new Error(response.message || 'Lỗi khi tạo kho');
    }

    return response.result;
  },

  /**
   * Cập nhật kho vật lý với validation
   */
  updatePhysicalWarehouse: async (
    warehouseId: number,
    data: UpdatePhysicalWarehouseDto
  ): Promise<PhysicalWarehouseDto> => {
    if (!warehouseId || warehouseId <= 0) {
      throw new Error('ID kho không hợp lệ');
    }

    // Validate dữ liệu nếu có
    if (data.name !== undefined && !data.name.trim()) {
      throw new Error('Tên kho không được để trống');
    }

    if (data.address !== undefined && !data.address.trim()) {
      throw new Error('Địa chỉ kho không được để trống');
    }

    if (data.capacity !== undefined && data.capacity < 0) {
      throw new Error('Sức chứa kho phải >= 0');
    }

    const response: ApiResponseDto<PhysicalWarehouseDto> = await apiRequest.put(
      `/user/physical-warehouses/${warehouseId}`,
      data
    );

    if (response.code !== 200) {
      throw new Error(response.message || 'Lỗi khi cập nhật kho vật lý');
    }

    return response.result;
  },

  /**
   * Xóa kho vật lý
   */
  deletePhysicalWarehouse: async (warehouseId: number): Promise<{ message: string }> => {
    if (!warehouseId || warehouseId <= 0) {
      throw new Error('ID kho không hợp lệ');
    }

    const response: ApiResponseDto<{ message: string }> = await apiRequest.delete(
      `/user/physical-warehouses/${warehouseId}`
    );

    if (response.code !== 200) {
      throw new Error(response.message || 'Lỗi khi xóa kho vật lý');
    }

    return response.result;
  },

  /**
   * Transform dữ liệu kho thành options cho AsyncSelectWithPagination
   */
  transformToSelectOptions: (warehouses: PhysicalWarehouseDto[]): SelectOption[] => {
    return warehouses.map((warehouse, index) => ({
      value: warehouse.warehouseId || index + 1, // Sử dụng index + 1 nếu không có warehouseId
      label: `${warehouse.name}${warehouse.address ? ` - ${warehouse.address}` : ''}`,
      data: warehouse as unknown as Record<string, unknown>,
    }));
  },

  /**
   * Lấy danh sách kho cho AsyncSelectWithPagination
   */
  getWarehousesForSelect: async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<WarehouseLoadOptionsResponse> => {
    const queryParams: QueryPhysicalWarehouseDto = {
      page: params.page || 1,
      limit: params.limit || 20,
      ...(params.search && { search: params.search }),
      sortBy: 'name',
      sortDirection: SortDirection.ASC,
    };

    const response = await WarehouseService.getPhysicalWarehouses(queryParams);

    return {
      items: WarehouseService.transformToSelectOptions(response.items),
      totalItems: response.meta.totalItems,
      totalPages: response.meta.totalPages,
      currentPage: response.meta.currentPage,
    };
  },
};
