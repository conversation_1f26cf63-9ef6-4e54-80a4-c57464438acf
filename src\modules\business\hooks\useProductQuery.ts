import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ProductService from '../services/product.service';
import {
  ProductQueryParams,
  CreateProductDto,
  UpdateProductDto,
  UpdateDigitalProductDto,
  UpdateEventProductDto,
  ProductTypeEnum,
} from '../types/product.types';

// Định nghĩa các query key
export const PRODUCT_QUERY_KEYS = {
  all: ['products'] as const,
  lists: () => [...PRODUCT_QUERY_KEYS.all, 'list'] as const,
  list: (filters: ProductQueryParams) => [...PRODUCT_QUERY_KEYS.lists(), filters] as const,
  details: () => [...PRODUCT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...PRODUCT_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách sản phẩm
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: PRODUCT_QUERY_KEYS.list(params || {}),
    queryFn: () => ProductService.getProducts(params),
    select: (data) => {
      return {
        items: data.items,
        meta: {
          totalItems: data.meta.totalItems,
          itemCount: data.meta.itemCount,
          itemsPerPage: data.meta.itemsPerPage,
          totalPages: data.meta.totalPages,
          currentPage: data.meta.currentPage,
        },
      };
    },
  });
};

/**
 * Hook để lấy chi tiết sản phẩm
 * @param id ID của sản phẩm
 * @param options Tùy chọn cho query
 * @returns Query object
 */
export const useProduct = (id?: number, options?: { staleTime?: number; gcTime?: number }) => {
  return useQuery({
    queryKey: PRODUCT_QUERY_KEYS.detail(id || 0),
    queryFn: () => ProductService.getProductById(id || 0),
    enabled: !!id, // Chỉ gọi API khi có ID
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // Default 5 minutes, có thể override
    gcTime: options?.gcTime ?? 10 * 60 * 1000, // Default 10 minutes, có thể override
  });
};

/**
 * Hook để tạo sản phẩm mới
 * @returns Mutation object
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => ProductService.createProduct(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook chuyên biệt để tạo sản phẩm EVENT với đầy đủ thông tin
 * @returns Mutation object với validation và logging chi tiết
 */
export const useCreateEventProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateProductDto) => {
      // Validate dữ liệu EVENT product
      if (data.productType !== ProductTypeEnum.EVENT) {
        throw new Error('Product type must be EVENT');
      }

      if (!data.advancedInfo) {
        throw new Error('advancedInfo is required for EVENT products');
      }

      const advancedInfo = data.advancedInfo as unknown as Record<string, unknown>;
      if (!advancedInfo['ticketTypes'] || !Array.isArray(advancedInfo['ticketTypes'])) {
        throw new Error('ticketTypes is required in advancedInfo for EVENT products');
      }

      const ticketTypes = advancedInfo['ticketTypes'] as Record<string, unknown>[];
      if (ticketTypes.length === 0) {
        throw new Error('At least one ticket type is required for EVENT products');
      }

      // Validate từng ticket type
      ticketTypes.forEach((ticket, index) => {
        if (!ticket['name'] || typeof ticket['name'] !== 'string' || (ticket['name'] as string).trim() === '') {
          throw new Error(`Ticket type ${index + 1}: name is required`);
        }
        if (typeof ticket['price'] !== 'number' || (ticket['price'] as number) < 0) {
          throw new Error(`Ticket type ${index + 1}: price must be a non-negative number`);
        }
        if (typeof ticket['quantity'] !== 'number' || (ticket['quantity'] as number) < 1) {
          throw new Error(`Ticket type ${index + 1}: quantity must be at least 1`);
        }
      });

      const response = await ProductService.createProduct(data);

      return response;
    },
    onSuccess: (data) => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

      // Invalidate cache cho sản phẩm cụ thể nếu có ID
      if (data.id) {
        queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.detail(data.id) });
      }
    },
    onError: (error) => {
      // Log chi tiết lỗi để debug
      if (error instanceof Error) {
        console.error('❌ [useCreateEventProduct] Error message:', error.message);
        console.error('❌ [useCreateEventProduct] Error stack:', error.stack);
      }
    },
  });
};

/**
 * Hook chuyên biệt để tạo sản phẩm dịch vụ (SERVICE) với validation và structure riêng
 * @returns Mutation object với validation và logging chi tiết cho SERVICE products
 */
export const useCreateServiceProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => {
      // Validate dữ liệu trước khi gửi
      if (!data.name || data.name.trim() === '') {
        throw new Error('Tên dịch vụ không được để trống');
      }

      if (!data.typePrice) {
        throw new Error('Loại giá không được để trống');
      }

      if (!data.price) {
        throw new Error('Thông tin giá không được để trống');
      }

      // Validate advancedInfo structure cho SERVICE
      if (!data.advancedInfo) {
        throw new Error('Thông tin nâng cao cho dịch vụ không được để trống');
      }

      const advancedInfo = data.advancedInfo as unknown as Record<string, unknown>;

      if (!advancedInfo['servicePackages'] || !Array.isArray(advancedInfo['servicePackages']) || (advancedInfo['servicePackages'] as unknown[]).length === 0) {
        throw new Error('Phải có ít nhất một gói dịch vụ');
      }

      // Validate service package structure
      const servicePackages = advancedInfo['servicePackages'] as Record<string, unknown>[];
      const servicePackage = servicePackages[0];

      if (!servicePackage) {
        throw new Error('Gói dịch vụ đầu tiên không tồn tại');
      }

      if (!servicePackage['name'] || typeof servicePackage['name'] !== 'string' || (servicePackage['name'] as string).trim() === '') {
        throw new Error('Tên gói dịch vụ không được để trống');
      }

      if (typeof servicePackage['price'] !== 'number' || (servicePackage['price'] as number) < 0) {
        throw new Error('Giá gói dịch vụ phải là số dương');
      }

      // Ensure proper structure for SERVICE products
      const cleanedData = {
        ...data,
        productType: ProductTypeEnum.SERVICE,
      } as CreateProductDto;
      return ProductService.createProduct(cleanedData);
    },
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

      // Note: Removed duplicate invalidations to prevent double API calls
      // PRODUCT_QUERY_KEYS.lists() already covers the necessary invalidation
    },
    onError: (error) => {
      // Log chi tiết lỗi để debug
      if (error instanceof Error) {
        console.error('❌ [useCreateServiceProduct] Error message:', error.message);
        console.error('❌ [useCreateServiceProduct] Error stack:', error.stack);
      }
    },
  });
};

/**
 * Hook để cập nhật sản phẩm
 * @returns Mutation object với hỗ trợ đầy đủ cho tất cả loại sản phẩm
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProductDto }) => {
      // Validate dữ liệu trước khi gửi
      if (!id || id <= 0) {
        throw new Error('ID sản phẩm không hợp lệ');
      }
      return ProductService.updateProduct(id, data);
    },
    onSuccess: (_, variables) => {
      // Invalidate both detail and lists to ensure fresh data after update
      // This is especially important for products with image uploads
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

      // Note: Changed from setQueryData to invalidateQueries to ensure fresh data
      // This prevents cache inconsistency issues with images after upload
    },
    onError: (error, variables) => {
      console.error('❌ [useUpdateProduct] Error updating product:', error);
      console.error('❌ [useUpdateProduct] Variables:', variables);
    },
  });
};

/**
 * Hook chuyên biệt để cập nhật sản phẩm sự kiện (EVENT) - loại bỏ inventory
 * @returns Mutation object với validation và logging chi tiết cho EVENT products
 */
export const useUpdateEventProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateEventProductDto }) => {
      // Validate dữ liệu trước khi gửi
      if (!id || id <= 0) {
        throw new Error('ID sản phẩm không hợp lệ');
      }

      if (!data.name || data.name.trim() === '') {
        throw new Error('Tên sản phẩm không được để trống');
      }

      if (!data.advancedInfo) {
        throw new Error('Thông tin nâng cao cho sản phẩm sự kiện không được để trống');
      }

      // Validate advancedInfo structure cho EVENT
      const advancedInfo = data.advancedInfo as unknown as Record<string, unknown>;

      if (!advancedInfo['startDate']) {
        throw new Error('Thời gian bắt đầu sự kiện không được để trống');
      }

      if (!advancedInfo['eventLocation'] && advancedInfo['eventFormat'] !== 'ONLINE') {
        throw new Error('Địa điểm sự kiện không được để trống cho sự kiện offline');
      }

      if (!advancedInfo['eventLink'] && advancedInfo['eventFormat'] === 'ONLINE') {
        throw new Error('Link sự kiện không được để trống cho sự kiện online');
      }

      if (!advancedInfo['ticketTypes'] || !Array.isArray(advancedInfo['ticketTypes']) || (advancedInfo['ticketTypes'] as unknown[]).length === 0) {
        throw new Error('Phải có ít nhất một loại vé cho sự kiện');
      }

      // Loại bỏ hoàn toàn inventory và shipmentConfig cho EVENT products
      const cleanedData = { ...data } as Record<string, unknown>;
      delete cleanedData['inventory'];
      delete cleanedData['shipmentConfig'];

      return ProductService.updateProduct(id, cleanedData);
    },
    onSuccess: (_, variables) => {
      // Invalidate both detail and lists to ensure fresh data after update
      // This is especially important for event products with ticket images
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

      // Note: Changed from setQueryData to invalidateQueries to ensure fresh data
      // This prevents cache inconsistency issues with ticket images after upload
    },
    onError: (error) => {
      // Log chi tiết lỗi để debug
      if (error instanceof Error) {
        console.error('❌ [useUpdateEventProduct] Error message:', error.message);
        console.error('❌ [useUpdateEventProduct] Error stack:', error.stack);
      }
    },
  });
};

/**
 * Hook chuyên biệt để cập nhật sản phẩm số với đầy đủ thông tin
 * @returns Mutation object với validation và logging chi tiết
 */
export const useUpdateDigitalProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateDigitalProductDto }) => {
      // Log để debug
      // Validate dữ liệu trước khi gửi
      if (!id || id <= 0) {
        throw new Error('ID sản phẩm không hợp lệ');
      }

      if (!data.name || data.name.trim() === '') {
        throw new Error('Tên sản phẩm không được để trống');
      }

      if (!data.price) {
        throw new Error('Giá sản phẩm không được để trống');
      }

      if (!data.advancedInfo) {
        throw new Error('Thông tin nâng cao cho sản phẩm số không được để trống');
      }

      // Validate advancedInfo structure
      const { digitalFulfillmentFlow, digitalOutput } = data.advancedInfo;

      if (!digitalFulfillmentFlow?.deliveryMethod) {
        throw new Error('Phương thức giao hàng không được để trống');
      }

      if (!digitalFulfillmentFlow?.deliveryTiming) {
        throw new Error('Thời gian giao hàng không được để trống');
      }

      if (!digitalOutput?.outputType) {
        throw new Error('Loại đầu ra sản phẩm số không được để trống');
      }

      if (!digitalOutput?.accessLink || digitalOutput.accessLink.trim() === '') {
        throw new Error('Link truy cập không được để trống');
      }

      return ProductService.updateProduct(id, data as UpdateProductDto);
    },
    onSuccess: (_, variables) => {
      // Invalidate both detail and lists to ensure fresh data after update
      // This is especially important for digital products with image uploads
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

      // Note: Changed from setQueryData to invalidateQueries to ensure fresh data
      // This prevents cache inconsistency issues with variant images after upload
    },
    onError: (error) => {
      // Log chi tiết lỗi để debug
      if (error instanceof Error) {
        console.error('❌ [useUpdateDigitalProduct] Error message:', error.message);
        console.error('❌ [useUpdateDigitalProduct] Error stack:', error.stack);
      }
    },
  });
};

/**
 * Hook để xóa sản phẩm
 * @returns Mutation object
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ProductService.deleteProduct(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để xóa nhiều sản phẩm
 * @returns Mutation object
 */
export const useDeleteMultipleProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => ProductService.deleteMultipleProducts(ids),
    onSuccess: () => {
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để lấy URL presigned cho upload hình ảnh
 * @returns Mutation object
 */
export const useGetPresignedUrl = () => {
  return useMutation({
    mutationFn: (mimeType: string) => ProductService.getPresignedUrl(mimeType),
  });
};

/**
 * Hook tổng hợp cho quản lý dữ liệu sản phẩm
 * @returns Các hooks liên quan đến sản phẩm
 */
export const useProductData = () => {
  return {
    useProducts,
    useProduct,
    useCreateProduct,
    useCreateServiceProduct,
    useUpdateProduct,
    useUpdateEventProduct,
    useUpdateDigitalProduct,
    useDeleteProduct,
    useDeleteMultipleProducts,
    useGetPresignedUrl,
  };
};

export default useProductData;
