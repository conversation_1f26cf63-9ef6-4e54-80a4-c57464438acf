import React from 'react';
import { GenericCustomFieldSelector, GenericCustomFieldData } from '@/shared/components/common';
import { useCustomFieldSearch, CustomFieldSearchItem } from '../hooks/useCustomFieldSearch';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface BusinessCustomFieldData extends GenericCustomFieldData {}

interface BusinessCustomFieldSelectorProps {
  onFieldSelect: (fieldData: BusinessCustomFieldData) => void;
  selectedFieldIds: number[];
  placeholder?: string;
}

/**
 * Business Custom Field Selector - wrapper cho GenericCustomFieldSelector
 * Sử dụng business API và logic
 */
const BusinessCustomFieldSelector: React.FC<BusinessCustomFieldSelectorProps> = ({
  onFieldSelect,
  selectedFieldIds,
  placeholder = 'Nhập từ khóa và nhấn Enter để tìm kiếm...',
}) => {
  const customFieldSearchHook = useCustomFieldSearch();

  // Wrapper function để transform business search function
  const searchFunction = async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      // Use the search function from the hook
      customFieldSearchHook.search(params.search || '');

      // Return the current state as the expected format
      return {
        items: customFieldSearchHook.items.map((item: CustomFieldSearchItem) => ({
          id: item.id,
          label: item.label,
          dataType: item.type, // Add dataType property required by GenericCustomFieldData
          type: item.type,
          component: item.component,
          configId: item.configId,
          required: item.required,
        })),
        totalItems: customFieldSearchHook.items.length,
        totalPages: 1,
        currentPage: 1,
        hasNextPage: customFieldSearchHook.hasMore,
      };
    } catch (error) {
      console.error('Error in business custom field search:', error);
      throw error;
    }
  };

  return (
    <GenericCustomFieldSelector
      onFieldSelect={onFieldSelect}
      selectedFieldIds={selectedFieldIds}
      placeholder={placeholder}
      searchFunction={searchFunction}
      title="Trường tùy chỉnh"
      translationNamespace="business"
    />
  );
};

export default BusinessCustomFieldSelector;
