import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getUserAddresses,
  getDefaultUserAddress,
  getUserAddressById,
  createUserAddress,
  updateUserAddress,
  deleteUserAddress,
  setDefaultUserAddress,
  CreateUserAddressDto,
  UpdateUserAddressDto,
} from '../services/user-address.api';

/**
 * Query keys cho user address
 */
export const USER_ADDRESS_QUERY_KEYS = {
  all: ['user', 'addresses'] as const,
  lists: () => [...USER_ADDRESS_QUERY_KEYS.all, 'list'] as const,
  list: () => [...USER_ADDRESS_QUERY_KEYS.lists()] as const,
  details: () => [...USER_ADDRESS_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_ADDRESS_QUERY_KEYS.details(), id] as const,
  default: () => [...USER_ADDRESS_QUERY_KEYS.all, 'default'] as const,
};

/**
 * Hook lấy danh sách địa chỉ của user hiện tại
 */
export const useUserAddresses = () => {
  return useQuery({
    queryKey: USER_ADDRESS_QUERY_KEYS.list(),
    queryFn: async () => {
      try {
        const response = await getUserAddresses();
        console.log('getUserAddresses response:', response);

        // Kiểm tra cấu trúc response
        if (response && response.result) {
          // Nếu result là array trực tiếp
          if (Array.isArray(response.result)) {
            return response.result;
          }
          // Nếu result là object có chứa items array (paginated response)
          if (response.result.items && Array.isArray(response.result.items)) {
            return response.result.items;
          }
        }

        console.warn('getUserAddresses API returned unexpected result structure:', response);
        return [];
      } catch (error) {
        console.error('Error fetching user addresses:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy địa chỉ mặc định của user hiện tại
 */
export const useDefaultUserAddress = () => {
  return useQuery({
    queryKey: USER_ADDRESS_QUERY_KEYS.default(),
    queryFn: async () => {
      const response = await getDefaultUserAddress();
      return response.result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy thông tin địa chỉ theo ID
 */
export const useUserAddress = (id: number, enabled = true) => {
  return useQuery({
    queryKey: USER_ADDRESS_QUERY_KEYS.detail(id),
    queryFn: async () => {
      const response = await getUserAddressById(id);
      return response.result;
    },
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook tạo địa chỉ mới
 */
export const useCreateUserAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserAddressDto) => createUserAddress(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách địa chỉ
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.default() });
    },
  });
};

/**
 * Hook cập nhật địa chỉ
 */
export const useUpdateUserAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateUserAddressDto }) => 
      updateUserAddress(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.default() });
    },
  });
};

/**
 * Hook xóa địa chỉ
 */
export const useDeleteUserAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteUserAddress(id),
    onSuccess: (_, id) => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.default() });
    },
  });
};

/**
 * Hook đặt địa chỉ làm mặc định
 */
export const useSetDefaultUserAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => setDefaultUserAddress(id),
    onSuccess: (_, id) => {
      // Invalidate và refetch
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: USER_ADDRESS_QUERY_KEYS.default() });
    },
  });
};
