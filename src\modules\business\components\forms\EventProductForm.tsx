import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormMultiWrapper,
  IconCard,
} from '@/shared/components/common';
import { z } from 'zod';
import {
  StringPriceDto,
  CreateProductDto,
  ProductDto,
  EventTicketType,
  EventProductConfig,
  ProductTypeEnum,
  PriceTypeEnum,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS, useCreateEventProduct } from '../../hooks/useProductQuery';
import {
  GeneralInfoSection,
  EventInfoSection,
  EventTicketTypesSection,
  MediaSection,
  CustomFieldsSection,
} from './sections';
import {
  SelectedCustomField,
  FormEventTicketType,
  EventProductFormValues,
} from './sections/event-product-form-types';


interface EventProductFormProps {
  onCancel: () => void;
  onSuccess?: (product: ProductDto) => void;
}



/**
 * Form tạo sự kiện
 */
const EventProductForm: React.FC<EventProductFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['business', 'common']);

  // Hook để tạo event product
  const createEventProductMutation = useCreateEventProduct();

  // Schema validation cho sự kiện
  const eventProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sự kiện không được để trống'),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Event product specific validations
      eventDateTime: z.date().optional(),
      eventLocation: z.string().optional(),
      attendanceMode: z.enum(['ONLINE', 'OFFLINE']),
      zoomLink: z.string().url('Link Zoom không hợp lệ').optional().or(z.literal('')),
      ticketTypes: z.array(z.object({
        id: z.string().optional(),
        name: z.string().min(1, 'Tên loại vé không được để trống'),
        price: z.number().min(0, 'Giá vé phải >= 0'),
        currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
        totalTickets: z.number().min(1, 'Tổng số vé phải >= 1'),
        saleStartTime: z.date().optional(),
        saleEndTime: z.date().optional(),
        ticketImage: z.string().optional(),
        sku: z.string().optional(),
        minQuantityPerOrder: z.number().min(1, 'Số vé tối thiểu phải >= 1'),
        maxQuantityPerOrder: z.number().min(1, 'Số vé tối đa phải >= 1'),
        description: z.string().optional(),
      })).min(1, 'Phải có ít nhất 1 loại vé'),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra địa điểm sự kiện cho offline events
      if (data.attendanceMode === 'OFFLINE' && (!data.eventLocation || data.eventLocation.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập địa điểm sự kiện cho hình thức offline',
          path: ['eventLocation'],
        });
      }

      // Kiểm tra Zoom link cho sự kiện online
      if (data.attendanceMode === 'ONLINE' && data.zoomLink && data.zoomLink.trim() !== '') {
        try {
          new URL(data.zoomLink);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Link Zoom không hợp lệ',
            path: ['zoomLink'],
          });
        }
      }

      // Kiểm tra ticket types
      if (!data.ticketTypes || data.ticketTypes.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Phải có ít nhất 1 loại vé',
          path: ['ticketTypes'],
        });
      } else {
        data.ticketTypes.forEach((ticket, index) => {
          if (!ticket.name || ticket.name.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `Tên loại vé ${index + 1} không được để trống`,
              path: ['ticketTypes', index, 'name'],
            });
          }
          if (ticket.maxQuantityPerOrder < ticket.minQuantityPerOrder) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `Số vé tối đa phải >= số vé tối thiểu cho loại vé ${index + 1}`,
              path: ['ticketTypes', index, 'maxQuantityPerOrder'],
            });
          }
        });
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho loại vé sự kiện
  const [ticketTypes, setTicketTypes] = useState<FormEventTicketType[]>([
    {
      id: `ticket-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      totalTickets: 1,
      minQuantityPerOrder: 1,
      maxQuantityPerOrder: 10,
    }
  ]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Đồng bộ ticketTypes state với form field
  useEffect(() => {
    if (formRef.current) {
      formRef.current.setValues({ ticketTypes });
    }
  }, [ticketTypes]);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 EventProductForm handleSubmit called with values:', values);

    // Đồng bộ ticketTypes state với form values trước khi validate
    const formValues = {
      ...values,
      ticketTypes: ticketTypes,
    } as EventProductFormValues;

    if (!formValues.name) {
      console.error('❌ Missing required fields:', {
        name: formValues.name,
      });
      NotificationUtil.error({
        message: t('business:product.form.validation.eventRequiredFieldsMissing'),
        duration: 3000,
      });
      return;
    }

    // Kiểm tra ticket types
    const validTicketTypes = ticketTypes.filter(ticket => ticket.name.trim() !== '');
    if (validTicketTypes.length === 0) {
      NotificationUtil.error({
        message: t('business:product.form.validation.ticketTypesRequired'),
        duration: 3000,
      });
      return;
    }

    try {
      const finalFormValues = formValues as EventProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', finalFormValues);

      // Tạo advancedInfo theo API structure cho EVENT product
      const advancedInfo: EventProductConfig = {
        purchaseCount: 0,
        eventFormat: (finalFormValues.attendanceMode === 'ONLINE' ? 'ONLINE' : finalFormValues.attendanceMode === 'OFFLINE' ? 'OFFLINE' : 'HYBRID') as 'ONLINE' | 'OFFLINE' | 'HYBRID',
        eventLink: finalFormValues.zoomLink || 'https://zoom.us/j/123456789',
        eventLocation: finalFormValues.eventLocation || t('business:product.form.eventProduct.defaultEventLocation'),
        startDate: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() : Date.now(),
        endDate: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() + (24 * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000), // +1 day
        timezone: 'Asia/Ho_Chi_Minh',
        ticketTypes: validTicketTypes.map(ticket => ({
          name: ticket.name,
          price: ticket.price,
          startTime: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() : Date.now(),
          endTime: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() + (24 * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000),
          timezone: 'Asia/Ho_Chi_Minh',
          description: ticket.description || t('business:product.form.eventProduct.ticketTypes.defaultTicketDescription'),
          quantity: ticket.totalTickets || 100,
          minQuantityPerPurchase: ticket.minQuantityPerOrder || 1,
          maxQuantityPerPurchase: ticket.maxQuantityPerOrder || 5,
          status: 'PENDING' as const,
          // Thêm imagesMediaTypes cho ticket có ảnh
          imagesMediaTypes: ticket.ticketImage && typeof ticket.ticketImage === 'string' && ticket.ticketImage.startsWith('blob:')
            ? ['image/jpeg']
            : [],
        })) as EventTicketType[],
      };

      // Tạo productData với conditional properties để tránh lỗi exactOptionalPropertyTypes
      const productData: CreateProductDto = {
        name: finalFormValues.name,
        productType: ProductTypeEnum.EVENT,
        price: {
          priceDescription: validTicketTypes.length > 0
            ? `Từ ${Math.min(...validTicketTypes.map(t => t.price)).toLocaleString()} VND`
            : 'Liên hệ'
        } as StringPriceDto,
        typePrice: PriceTypeEnum.STRING_PRICE,
        advancedInfo,
      };

      // Thêm các properties optional chỉ khi có giá trị
      if (finalFormValues.description && finalFormValues.description.trim()) {
        productData.description = finalFormValues.description.trim();
      }

      if (finalFormValues.tags && finalFormValues.tags.length > 0) {
        productData.tags = finalFormValues.tags;
      }

      // Tính số ảnh vé cần upload
      const ticketImagesCount = validTicketTypes.filter(ticket =>
        ticket.ticketImage &&
        typeof ticket.ticketImage === 'string' &&
        ticket.ticketImage.startsWith('blob:')
      ).length;

      console.log('🎫 [EventProductForm] Image count calculation:', {
        productImages: mediaFiles.length,
        ticketImages: ticketImagesCount
      });

      // Chỉ tạo imagesMediaTypes cho ảnh sản phẩm chính, không bao gồm ảnh vé
      if (mediaFiles.length > 0) {
        const productImageTypes = mediaFiles.map(file => file.file.type);
        productData.imagesMediaTypes = productImageTypes;

        console.log('🎫 [EventProductForm] Product imagesMediaTypes:', productData.imagesMediaTypes);
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
          const fieldValue = field.value?.['value'];

          // Nếu là undefined hoặc null thì loại bỏ
          if (fieldValue === undefined || fieldValue === null) {
            return false;
          }

          // Nếu là string rỗng thì loại bỏ
          if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
            return false;
          }

          // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
          return true;
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Gọi API để tạo event product
      const response = await createEventProductMutation.mutateAsync(productData);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const responseObj = response as unknown as Record<string, unknown>;
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            responseObj['uploadUrls'] &&
            typeof responseObj['uploadUrls'] === 'object' &&
            'imagesUploadUrls' in (responseObj['uploadUrls'] as Record<string, unknown>) &&
            Array.isArray((responseObj['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls']);

          if (hasUploadUrls) {
            const allUploadUrls = (responseObj['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls'] as Array<{
              url: string;
              key: string;
              index: number;
            }>;

            console.log('🎫 [EventProductForm] All upload URLs received:', {
              totalUrls: allUploadUrls.length,
              productImagesNeeded: mediaFiles.length,
              ticketImagesNeeded: ticketImagesCount
            });

            if (allUploadUrls.length > 0 && mediaFiles.length > 0) {
              // Lấy upload URLs cho product images (các URLs đầu tiên)
              const productUploadUrls = allUploadUrls.slice(0, mediaFiles.length);

              console.log('🎫 [EventProductForm] Product upload URLs:', {
                count: productUploadUrls.length,
                urls: productUploadUrls.map(u => ({ index: u.index, url: u.url.substring(0, 50) + '...' }))
              });

              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.map((fileData, index) => {
                const uploadInfo = productUploadUrls[index];
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);
              for (let i = 0; i < filesToUpload.length; i++) {
                const fileToUpload = filesToUpload[i];
                const urlToUpload = urlsToUpload[i];

                if (!fileToUpload || !urlToUpload) {
                  console.warn(`⚠️ Missing file or URL at index ${i}`);
                  continue;
                }

                await uploadProductImages([fileToUpload], [urlToUpload], {
                  skipCacheInvalidation: true,
                });
              }

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch {
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // Upload ảnh cho ticket types sử dụng advancedImagesUploadUrls
      console.log('🎫 [EventProductForm] Checking for ticket image uploads...');

      // Lấy upload URLs cho ticket images từ advancedImagesUploadUrls
      const responseObj = response as unknown as Record<string, unknown>;
      const hasAdvancedUploadUrls =
        response &&
        typeof response === 'object' &&
        'uploadUrls' in response &&
        responseObj['uploadUrls'] &&
        typeof responseObj['uploadUrls'] === 'object' &&
        'advancedImagesUploadUrls' in (responseObj['uploadUrls'] as Record<string, unknown>) &&
        Array.isArray((responseObj['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls']);

      if (hasAdvancedUploadUrls) {
        const ticketUploadUrls = (responseObj['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls'] as Array<{
          url: string;
          key: string;
          type: string;
          index: number;
          position: number;
        }>;

        console.log('🎫 [EventProductForm] Ticket upload URLs:', {
          ticketUrlsAvailable: ticketUploadUrls.length,
          ticketImagesNeeded: ticketImagesCount,
          ticketUploadUrls: ticketUploadUrls.map(u => ({ type: u.type, index: u.index, position: u.position }))
        });

        // Lọc các ticket types có ảnh cần upload (chỉ blob URLs)
        const ticketTypesWithImages = ticketTypes.filter(ticket =>
          ticket.ticketImage &&
          typeof ticket.ticketImage === 'string' &&
          ticket.ticketImage.startsWith('blob:')
        );

        console.log('🎫 [EventProductForm] Ticket types with images to upload:', {
          totalTicketTypes: ticketTypes.length,
          ticketTypesWithImages: ticketTypesWithImages.length,
          ticketTypesData: ticketTypesWithImages.map(t => ({
            id: t.id,
            name: t.name,
            hasImage: !!t.ticketImage,
            imageType: t.ticketImage?.startsWith('blob:') ? 'blob' : 'url'
          }))
        });

        if (ticketTypesWithImages.length > 0 && ticketUploadUrls.length >= ticketTypesWithImages.length) {
          try {
            let uploadedCount = 0;

            // Upload ảnh cho từng ticket type sử dụng advancedImagesUploadUrls từ backend
            for (let ticketIndex = 0; ticketIndex < ticketTypesWithImages.length; ticketIndex++) {
              const ticket = ticketTypesWithImages[ticketIndex];
              const ticketUploadUrl = ticketUploadUrls[ticketIndex];

              if (!ticket || !ticketUploadUrl) {
                console.warn(`⚠️ [EventProductForm] Missing ticket or upload URL at index ${ticketIndex}`);
                continue;
              }

              console.log(`🎫 [EventProductForm] Processing ticket ${ticketIndex + 1}/${ticketTypesWithImages.length}: ${ticket.name}`);
              console.log(`🎫 [EventProductForm] Upload URL info:`, {
                type: ticketUploadUrl.type,
                index: ticketUploadUrl.index,
                position: ticketUploadUrl.position,
                key: ticketUploadUrl.key
              });

              if (ticket.ticketImage) {
                try {
                  console.log(`🎫 [EventProductForm] Fetching blob for ticket "${ticket.name}": ${ticket.ticketImage.substring(0, 50)}...`);

                  const fetchResponse = await fetch(ticket.ticketImage);
                  const blob = await fetchResponse.blob();
                  const file = new File([blob], `ticket-${ticket.id}.jpg`, { type: 'image/jpeg' });

                  console.log(`🎫 [EventProductForm] Created file for ticket "${ticket.name}":`, {
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type
                  });

                  console.log(`🎫 [EventProductForm] Uploading ticket image for "${ticket.name}" to: ${ticketUploadUrl.url.substring(0, 50)}...`);

                  await uploadProductImages([{ file, id: `ticket-${ticket.id}` }], [ticketUploadUrl.url], {
                    skipCacheInvalidation: true,
                  });

                  uploadedCount++;
                  console.log(`✅ [EventProductForm] Successfully uploaded image for ticket "${ticket.name}" (${uploadedCount}/${ticketTypesWithImages.length})`);

                } catch (ticketUploadError) {
                  console.error(`❌ [EventProductForm] Error uploading ticket type image for ${ticket.name}:`, ticketUploadError);
                }
              }
            }

            if (uploadedCount > 0) {
              NotificationUtil.success({
                message: t('business:product.form.validation.ticketUploadSuccess', `Đã tải lên ${uploadedCount} ảnh vé thành công`),
                duration: 3000,
              });
            }
          } catch (classificationUploadError) {
            console.error('❌ [EventProductForm] Error in ticket image upload process:', classificationUploadError);
            NotificationUtil.warning({
              message: t('business:product.form.validation.ticketUploadError', 'Có lỗi xảy ra khi tải lên ảnh vé'),
              duration: 5000,
            });
          }
        } else {
          console.log('ℹ️ [EventProductForm] No ticket images to upload or insufficient upload URLs:', {
            ticketImagesCount: ticketTypesWithImages.length,
            uploadUrlsCount: ticketUploadUrls.length
          });
        }
      } else {
        console.log('ℹ️ [EventProductForm] No advancedImagesUploadUrls available for ticket images');
      }

      setIsUploading(false);

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess(response);
      }

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.createSuccess', 'Tạo sự kiện thành công'),
        duration: 3000,
      });

      // Reset form sau khi tạo thành công
      if (formRef.current) {
        formRef.current.reset();
      }
      setTempTags([]);
      setMediaFiles([]);
      setProductCustomFields([]);
      setTicketTypes([
        {
          id: `ticket-${Date.now()}`,
          name: '',
          price: 0,
          currency: 'VND',
          totalTickets: 1,
          minQuantityPerOrder: 1,
          maxQuantityPerOrder: 10,
        }
      ]);

    } catch (error) {
      console.error('Error in EventProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError', 'Có lỗi xảy ra khi tạo sự kiện'),
        duration: 3000,
      });
    }
  };



  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm loại vé mới
  const handleAddTicketType = useCallback(() => {
    const newTicket: FormEventTicketType = {
      id: `ticket-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      totalTickets: 1,
      minQuantityPerOrder: 1,
      maxQuantityPerOrder: 10,
    };
    setTicketTypes(prev => [...prev, newTicket]);
  }, []);

  // Xóa loại vé
  const handleRemoveTicketType = useCallback((ticketId: string) => {
    setTicketTypes(prev => prev.filter(ticket => ticket.id !== ticketId));
  }, []);

  // Cập nhật thông tin loại vé
  const handleUpdateTicketType = useCallback((ticketId: string, field: keyof FormEventTicketType, value: string | number | Date) => {
    setTicketTypes(prev => prev.map(ticket => {
      if (ticket.id === ticketId) {
        return { ...ticket, [field]: value };
      }
      return ticket;
    }));
  }, []);

  // Upload ảnh cho loại vé
  const handleTicketImageUpload = useCallback((ticketId: string, file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setTicketTypes(prev => prev.map(ticket => {
      if (ticket.id === ticketId) {
        return { ...ticket, ticketImage: imageUrl };
      }
      return ticket;
    }));
  }, []);

  // Xóa ảnh loại vé
  const handleRemoveTicketImage = useCallback((ticketId: string) => {
    setTicketTypes(prev => prev.map(ticket => {
      if (ticket.id === ticketId) {
        // Revoke the blob URL to free memory
        if (ticket.ticketImage && ticket.ticketImage.startsWith('blob:')) {
          URL.revokeObjectURL(ticket.ticketImage);
        }
        const updatedTicket: FormEventTicketType = { ...ticket };
        delete updatedTicket.ticketImage;
        return updatedTicket;
      }
      return ticket;
    }));
  }, []);

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Event product defaults
      eventDateTime: undefined,
      eventLocation: '',
      attendanceMode: 'OFFLINE' as const,
      zoomLink: '',
      ticketTypes: [
        {
          id: `ticket-${Date.now()}`,
          name: '',
          price: 0,
          currency: 'VND',
          totalTickets: 1,
          minQuantityPerOrder: 1,
          maxQuantityPerOrder: 10,
        }
      ],
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createEventTitle', 'Tạo sự kiện')}>
      <Form
        ref={formRef}
        schema={eventProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || t('business:product.form.validation.formValidationError');
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <GeneralInfoSection tempTags={tempTags} setTempTags={setTempTags} />

        {/* 2. Thông tin sự kiện */}
        <EventInfoSection />

        {/* 3. Loại vé sự kiện */}
        <EventTicketTypesSection
          ticketTypes={ticketTypes}
          handleAddTicketType={handleAddTicketType}
          handleRemoveTicketType={handleRemoveTicketType}
          handleUpdateTicketType={handleUpdateTicketType}
          handleTicketImageUpload={handleTicketImageUpload}
          handleRemoveTicketImage={handleRemoveTicketImage}
        />

        {/* 4. Hình ảnh sự kiện */}
        <MediaSection
          mediaFiles={mediaFiles}
          setMediaFiles={setMediaFiles}
          tempTags={tempTags}
          setTempTags={setTempTags}
        />

        {/* 5. Trường tùy chỉnh */}
        <CustomFieldsSection
          productCustomFields={productCustomFields}
          setProductCustomFields={setProductCustomFields}
          handleToggleCustomFieldToProduct={handleToggleCustomFieldToProduct}
          handleUpdateCustomFieldInProduct={handleUpdateCustomFieldInProduct}
          handleRemoveCustomFieldFromProduct={handleRemoveCustomFieldFromProduct}
        />

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              createEventProductMutation.isPending || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo sự kiện')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={createEventProductMutation.isPending || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default EventProductForm;
