import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Icon,
  Chip,
  Loading,
  Alert,
} from '@/shared/components/common';
import { useOrderTracking } from '../hooks/useOrderQuery';
import { ShippingStatusEnum } from '../types/order.types';
import { formatTimestamp } from '@/shared/utils/date';

/**
 * Props cho OrderTracking component
 */
interface OrderTrackingProps {
  orderId: number;
  shippingStatus?: ShippingStatusEnum;
  carrier?: string;
  trackingNumber?: string;
  className?: string;
}

/**
 * Component hiển thị thông tin tracking đơn hàng
 */
const OrderTracking: React.FC<OrderTrackingProps> = ({
  orderId,
  shippingStatus,
  carrier,
  trackingNumber,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);
  const { data: trackingInfo, isLoading, error, refetch } = useOrderTracking(orderId);

  // Render trạng thái vận chuyển
  const renderShippingStatus = (status: ShippingStatusEnum) => {
    const statusConfig = {
      [ShippingStatusEnum.PENDING]: { 
        variant: 'warning', 
        label: t('business:order.shippingStatus.pending'),
        icon: 'clock'
      },
      [ShippingStatusEnum.PREPARING]: { 
        variant: 'info', 
        label: t('business:order.shippingStatus.preparing'),
        icon: 'package'
      },
      [ShippingStatusEnum.SHIPPED]: { 
        variant: 'info', 
        label: t('business:order.shippingStatus.shipped'),
        icon: 'truck'
      },
      [ShippingStatusEnum.IN_TRANSIT]: { 
        variant: 'info', 
        label: t('business:order.shippingStatus.inTransit'),
        icon: 'map-pin'
      },
      [ShippingStatusEnum.SORTING]: { 
        variant: 'info', 
        label: t('business:order.shippingStatus.sorting'),
        icon: 'shuffle'
      },
      [ShippingStatusEnum.DELIVERED]: { 
        variant: 'success', 
        label: t('business:order.shippingStatus.delivered'),
        icon: 'check-circle'
      },
      [ShippingStatusEnum.DELIVERY_FAILED]: { 
        variant: 'danger', 
        label: t('business:order.shippingStatus.deliveryFailed'),
        icon: 'x-circle'
      },
      [ShippingStatusEnum.RETURNING]: { 
        variant: 'warning', 
        label: t('business:order.shippingStatus.returning'),
        icon: 'rotate-ccw'
      },
      [ShippingStatusEnum.CANCELLED]: { 
        variant: 'danger', 
        label: t('business:order.shippingStatus.cancelled'),
        icon: 'x'
      },
    };
    
    const config = statusConfig[status] || { 
      variant: 'default', 
      label: status,
      icon: 'help-circle'
    };
    
    return (
      <div className="flex items-center space-x-2">
        <Icon name={config.icon as 'clock' | 'package' | 'truck' | 'map-pin' | 'shuffle' | 'check-circle' | 'x-circle' | 'rotate-ccw' | 'x' | 'help-circle'} size="sm" />
        <Chip variant={config.variant as 'warning' | 'info' | 'success' | 'danger' | 'default'}>{config.label}</Chip>
      </div>
    );
  };

  // Render timeline của tracking
  const renderTrackingTimeline = (tracking: { status?: string; lastUpdated?: number } | null) => {
    if (!tracking || !tracking.status) return null;

    // Các bước tracking cơ bản
    const trackingSteps = [
      { 
        status: ShippingStatusEnum.PENDING, 
        label: t('business:order.shippingStatus.pending'),
        icon: 'clock'
      },
      { 
        status: ShippingStatusEnum.PREPARING, 
        label: t('business:order.shippingStatus.preparing'),
        icon: 'package'
      },
      { 
        status: ShippingStatusEnum.SHIPPED, 
        label: t('business:order.shippingStatus.shipped'),
        icon: 'truck'
      },
      { 
        status: ShippingStatusEnum.IN_TRANSIT, 
        label: t('business:order.shippingStatus.inTransit'),
        icon: 'map-pin'
      },
      { 
        status: ShippingStatusEnum.DELIVERED, 
        label: t('business:order.shippingStatus.delivered'),
        icon: 'check-circle'
      },
    ];

    const currentStatusIndex = trackingSteps.findIndex(step => step.status === shippingStatus);

    return (
      <div className="space-y-4">
        <h4 className="font-medium">{t('business:order.trackingTimeline')}</h4>
        <div className="space-y-3">
          {trackingSteps.map((step, index) => {
            const isCompleted = index <= currentStatusIndex;
            const isCurrent = index === currentStatusIndex;
            
            return (
              <div key={step.status} className="flex items-center space-x-3">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center
                  ${isCompleted
                    ? 'bg-green-500 text-white'
                    : isCurrent
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }
                `}>
                  <Icon name={step.icon as 'clock' | 'package' | 'truck' | 'map-pin' | 'check-circle'} size="sm" />
                </div>
                <div className="flex-1">
                  <p className={`font-medium ${isCompleted ? 'text-green-600' : isCurrent ? 'text-blue-600' : 'text-gray-500'}`}>
                    {step.label}
                  </p>
                  {isCurrent && tracking.lastUpdated && (
                    <p className="text-sm text-gray-500">
                      {formatTimestamp(tracking.lastUpdated)}
                    </p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Card
      className={className}
      title={
        <div className="flex items-center space-x-2">
          <Icon name="map-pin" size="sm" />
          <span>{t('business:order.tracking')}</span>
        </div>
      }
      extra={
        <Button
          variant="ghost"
          size="sm"
          onClick={() => refetch()}
          disabled={isLoading}
        >
          <Icon name="refresh-cw" size="sm" className={isLoading ? 'animate-spin' : ''} />
        </Button>
      }
    >
      <>
        {/* Thông tin cơ bản */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {carrier && (
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.carrier')}
              </label>
              <p className="mt-1">{carrier}</p>
            </div>
          )}
          
          {trackingNumber && (
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.trackingNumber')}
              </label>
              <p className="mt-1 font-mono text-sm">{trackingNumber}</p>
            </div>
          )}
          
          {shippingStatus && (
            <div className="md:col-span-2">
              <label className="text-sm font-medium text-gray-700">
                {t('business:order.currentStatus')}
              </label>
              <div className="mt-1">
                {renderShippingStatus(shippingStatus)}
              </div>
            </div>
          )}
        </div>

        {/* Loading state */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <Loading size="md" />
            <span className="ml-2">{t('business:order.loadingTracking')}</span>
          </div>
        )}

        {/* Error state */}
        {error && (
          <Alert
            type="error"
            message={t('business:order.trackingError')}
            description={
              error instanceof Error
                ? error.message
                : typeof error === 'string'
                  ? error
                  : t('business:order.trackingErrorGeneric', 'Đã xảy ra lỗi khi tải thông tin tracking')
            }
            showIcon={true}
            className="mb-4"
          />
        )}

        {/* Tracking timeline */}
        {shippingStatus && !isLoading && (
          <div className="border-t pt-4">
            {renderTrackingTimeline(trackingInfo as { status?: string; lastUpdated?: number } | null)}
          </div>
        )}

        {/* Detailed tracking info */}
        {trackingInfo && !isLoading && (
          <div className="border-t pt-4 mt-4">
            <h4 className="font-medium mb-2">{t('business:order.detailedTracking')}</h4>
            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="text-sm text-gray-600 whitespace-pre-wrap overflow-auto max-h-64">
                {JSON.stringify(trackingInfo, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* No tracking info */}
        {!trackingInfo && !isLoading && !error && (
          <div className="text-center py-8 text-gray-500">
            <Icon name="file" size="lg" className="mx-auto mb-2 opacity-50" />
            <p>{t('business:order.noTrackingInfo')}</p>
          </div>
        )}
      </>
    </Card>
  );
};

export default OrderTracking;