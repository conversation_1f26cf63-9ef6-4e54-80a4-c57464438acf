import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../services/product.service';
import { CreateProductDto, CreateDigitalProductResponse, ProductTypeEnum } from '../types/product.types';
import { PRODUCT_QUERY_KEYS } from './useProductQuery';

export const useCreateDigitalProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateProductDto): Promise<CreateDigitalProductResponse> => {
      const response = await ProductService.createProduct(data);

      // Type assertion for response to handle dynamic properties
      const responseData = response as unknown as Record<string, unknown>;

      const digitalProductResponse: CreateDigitalProductResponse = {
        id: String(response.id), // Convert number to string if needed
        name: response.name,
        productType: response.productType || ProductTypeEnum.DIGITAL,
        typePrice: response.typePrice,
        price: response.price as {
          listPrice: number;
          salePrice: number;
          currency: string;
        },
        ...(response.description && { description: response.description }),
        ...(response.tags && { tags: response.tags }),
        ...(response.images && {
          images: response.images.map(img => ({
            key: img.key,
            position: img.position,
            url: img.url
          }))
        }),
        // Add uploadUrls with both main images and advanced images for variants
        ...(responseData['uploadUrls'] ? {
          uploadUrls: {
            productId: String((responseData['uploadUrls'] as Record<string, unknown>)['productId']),
            imagesUploadUrls: (responseData['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls'] as Array<{
              url: string;
              key: string;
              index: number;
            }> || [],
            ...((responseData['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls'] ? {
              advancedImagesUploadUrls: (responseData['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls'] as Array<{
                url: string;
                key: string;
                type: string;
                index: number;
                position: number;
              }>
            } : {})
          }
        } : {}),
        // Add metadata if it exists (contains variants and custom fields)
        ...(responseData['metadata'] ? {
          metadata: responseData['metadata'] as NonNullable<CreateDigitalProductResponse['metadata']>
        } : {}),
        // Keep classifications for backward compatibility
        ...(responseData['classifications'] ? {
          classifications: responseData['classifications'] as NonNullable<CreateDigitalProductResponse['classifications']>
        } : {}),
        // Keep advancedInfo if it exists
        ...(responseData['advancedInfo'] ? {
          advancedInfo: responseData['advancedInfo'] as NonNullable<CreateDigitalProductResponse['advancedInfo']>
        } : {}),
      };

      return digitalProductResponse;
    },
    onSuccess: (data) => {
      console.log('✅ [useCreateDigitalProduct] Product created successfully:', data);
      // Invalidate và refetch danh sách sản phẩm
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });
    },
    onError: (error) => {
      console.error('❌ [useCreateDigitalProduct] Error creating product:', error);
    },
  });
};
