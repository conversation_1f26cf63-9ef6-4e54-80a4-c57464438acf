import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Loading,
  Breadcrumb,
  Table,
  ActionMenu,
  Modal,
  ConfirmDeleteModal,
} from '@/shared/components';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useVirtualWarehouse } from '../../hooks/user-virtual-warehouse.hooks';
import { useRootFolders, useChildFolders, useDeleteFolder, useCreateFolder } from '../../hooks/user-folder.hooks';
import { useFilesByFolder, useDeleteFile } from '../../hooks/user-file.hooks';
import { useFileUploadWithQueue } from '../../hooks/useFileUploadWithQueue';
import { FolderResponseDto } from '../../services/user-folder.service';
import { NotificationUtil } from '@/shared/utils/notification';
import FileTable from './FileTable';

// Interface cho props của component
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface VirtualWarehouseDetailPageProps {}

/**
 * Trang xem chi tiết kho ảo với cấu trúc thư mục và file
 */
const VirtualWarehouseDetailPage: React.FC<VirtualWarehouseDetailPageProps> = () => {
  const { warehouseId } = useParams<{ warehouseId: string }>();
  const { t } = useTranslation(['business', 'common']);

  // State
  const [currentFolderId, setCurrentFolderId] = useState<number | null>(null);
  const [selectedFolders, setSelectedFolders] = useState<number[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteType, setDeleteType] = useState<'folder' | 'file'>('folder');
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{ id: number | null; name: string }>>([]);
  const [createFolderModalOpen, setCreateFolderModalOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [, setIsUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Queries
  const warehouseIdNum = warehouseId ? parseInt(warehouseId) : 0;
  const { data: warehouse, isLoading: warehouseLoading } = useVirtualWarehouse(warehouseIdNum);
  const { data: rootFolders, isLoading: rootLoading } = useRootFolders();
  const { data: childFolders, isLoading: childLoading } = useChildFolders(
    currentFolderId || 0,
    !!currentFolderId
  );
  const { data: files, isLoading: filesLoading } = useFilesByFolder(
    currentFolderId || 0,
    !!currentFolderId
  );

  // Mutations
  const { mutateAsync: deleteFolder } = useDeleteFolder();
  const { mutateAsync: deleteFile } = useDeleteFile();
  const { mutateAsync: createFolder } = useCreateFolder();

  // File upload với TaskQueue
  const { uploadFile, uploadFiles } = useFileUploadWithQueue();

  // Loading state
  const isLoading = warehouseLoading || rootLoading || childLoading || filesLoading;

  // Xử lý điều hướng thư mục
  const handleFolderClick = (folder: FolderResponseDto) => {
    setCurrentFolderId(folder.id);
    setBreadcrumbs(prev => [...prev, { id: folder.id, name: folder.name }]);
    setSelectedFolders([]);
    setSelectedFiles([]);
  };

  // Xử lý quay lại thư mục cha
  const handleBreadcrumbClick = (index: number) => {
    if (index === 0) {
      // Quay về root
      setCurrentFolderId(null);
      setBreadcrumbs([]);
    } else {
      // Quay về thư mục cha
      const targetBreadcrumb = breadcrumbs[index - 1];
      if (targetBreadcrumb) {
        setCurrentFolderId(targetBreadcrumb.id);
        setBreadcrumbs(prev => prev.slice(0, index));
      }
    }
    setSelectedFolders([]);
    setSelectedFiles([]);
  };

  // Xử lý xóa items
  const handleDelete = async () => {
    try {
      if (deleteType === 'folder') {
        await Promise.all(selectedFolders.map(id => deleteFolder(id)));
        NotificationUtil.success({
          message: t('business:folder.deleteSuccess', 'Xóa thư mục thành công'),
        });
        setSelectedFolders([]);
      } else {
        await Promise.all(selectedFiles.map(id => deleteFile(id)));
        NotificationUtil.success({
          message: t('business:file.deleteSuccess', 'Xóa file thành công'),
        });
        setSelectedFiles([]);
      }
      setDeleteModalOpen(false);
    } catch (error) {
      console.error('Delete error:', error);
      NotificationUtil.error({
        message: t('business:common.deleteError', 'Có lỗi xảy ra khi xóa'),
      });
    }
  };

  // Xử lý xóa folder đơn lẻ
  const handleDeleteFolder = (folderId: number) => {
    setSelectedFolders([folderId]);
    setDeleteType('folder');
    setDeleteModalOpen(true);
  };

  // Xử lý xóa file đơn lẻ
  const handleDeleteFile = (fileId: number) => {
    setSelectedFiles([fileId]);
    setDeleteType('file');
    setDeleteModalOpen(true);
  };

  // Xử lý xem file
  const handleViewFile = (fileId: number) => {
    // TODO: Implement file viewer
    console.log('View file:', fileId);
  };

  // Xử lý tạo thư mục mới
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      NotificationUtil.error({
        message: t('business:folder.nameRequired', 'Tên thư mục không được để trống'),
      });
      return;
    }

    try {
      await createFolder({
        name: newFolderName.trim(),
        parentId: currentFolderId,
        root: warehouseIdNum, // Truyền warehouseId vào trường root
      });

      setNewFolderName('');
      setCreateFolderModalOpen(false);
      NotificationUtil.success({
        message: t('business:folder.createSuccess', 'Tạo thư mục thành công'),
      });
    } catch (error) {
      console.error('Create folder error:', error);
      NotificationUtil.error({
        message: t('business:folder.createError', 'Có lỗi xảy ra khi tạo thư mục'),
      });
    }
  };

  // Xử lý search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Xử lý bulk delete folders
  const handleBulkDeleteFolders = () => {
    if (selectedFolders.length > 0) {
      setDeleteType('folder');
      setDeleteModalOpen(true);
    }
  };

  // Xử lý bulk delete files
  const handleBulkDeleteFiles = () => {
    if (selectedFiles.length > 0) {
      setDeleteType('file');
      setDeleteModalOpen(true);
    }
  };

  // Xử lý upload file
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);

    try {
      const fileArray = Array.from(files);

      if (fileArray.length === 1) {
        // Upload single file
        const firstFile = fileArray[0];
        if (firstFile) {
          await uploadFile({
            file: firstFile,
            ...(currentFolderId && { folderId: currentFolderId }),
            warehouseId: warehouseIdNum,
          });
        }
      } else {
        // Upload multiple files
        await uploadFiles(
          fileArray,
          currentFolderId ?? undefined,
          warehouseIdNum
        );
      }

      // Success notification sẽ được hiển thị từ hook
    } catch (error) {
      console.error('Upload error:', error);
      // Error notification sẽ được hiển thị từ hook
    } finally {
      setIsUploading(false);
      // Reset file input
      if (event.target) {
        event.target.value = '';
      }
    }
  };

  // Chuẩn bị dữ liệu cho table với filter
  const currentFolders = (currentFolderId ? childFolders || [] : rootFolders || [])
    .filter(folder =>
      !searchTerm || folder.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  const currentFiles = (currentFolderId ? files || [] : [])
    .filter(file =>
      !searchTerm || file.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

  // Columns cho table thư mục
  const folderColumns = [
    {
      key: 'name',
      title: t('business:folder.name', 'Tên thư mục'),
      render: (_: unknown, folder: FolderResponseDto) => (
        <div className="flex items-center space-x-2">
          <Icon name="folder" className="text-blue-500" />
          <button
            onClick={() => handleFolderClick(folder)}
            className="text-blue-600 hover:text-blue-800 hover:underline"
          >
            {folder.name}
          </button>
        </div>
      ),
    },
    {
      key: 'path',
      title: t('business:folder.path', 'Đường dẫn'),
      render: (_: unknown, folder: FolderResponseDto) => folder.path || '/',
    },
    {
      key: 'createdAt',
      title: t('common:createdAt', 'Ngày tạo'),
      render: (_: unknown, folder: FolderResponseDto) => {
        const formatTimestamp = (timestamp: string | number) => {
          try {
            // Convert string timestamp to number if needed
            const timeValue = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

            if (isNaN(timeValue)) {
              return 'Invalid Date';
            }

            const date = new Date(timeValue);

            if (isNaN(date.getTime())) {
              return 'Invalid Date';
            }

            return date.toLocaleDateString('vi-VN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
            });
          } catch (error) {
            console.error('Error formatting timestamp:', error, timestamp);
            return 'Invalid Date';
          }
        };

        return formatTimestamp(folder.createdAt);
      },
    },
    {
      key: 'actions',
      title: t('common:actions', 'Thao tác'),
      render: (_: unknown, folder: FolderResponseDto) => (
        <ActionMenu
          items={[
            {
              id: 'edit',
              label: t('common:edit', 'Sửa'),
              icon: 'edit',
              onClick: () => {
                // TODO: Implement edit folder
              },
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDeleteFolder(folder.id),
            },
          ]}
        />
      ),
    },
  ];

  if (isLoading) {
    return <Loading />;
  }

  if (!warehouse) {
    return (
      <div className="text-center p-8">
        <Typography variant="h6">{t('business:warehouse.notFound')}</Typography>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Breadcrumb
            items={[
              { label: t('business:warehouse.title'), path: '/business/virtual-warehouse' },
              { label: warehouse.name },
              ...breadcrumbs.map((crumb, index) => ({
                label: crumb.name,
                onClick: () => handleBreadcrumbClick(index + 1),
              })),
            ]}
          />
          <Typography variant="h4" className="mt-2">
            {warehouse.name}
          </Typography>
          {warehouse.description && (
            <Typography variant="body2" className="text-gray-600 mt-1">
              {warehouse.description}
            </Typography>
          )}
        </div>
      </div>

      {/* Warehouse Info */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('business:warehouse.associatedSystem')}
            </Typography>
            <Typography variant="body1">
              {warehouse.associatedSystem || t('common:notSet')}
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('business:warehouse.purpose')}
            </Typography>
            <Typography variant="body1">
              {warehouse.purpose || t('common:notSet')}
            </Typography>
          </div>
        </div>
      </Card>

      {/* Folders and Files */}
      <Card
        title={
          currentFolderId
            ? t('business:folder.contents', 'Nội dung thư mục')
            : t('business:folder.rootFolders', 'Thư mục gốc')
        }
      >
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => setCreateFolderModalOpen(true)}
            items={[]}
            showDateFilter={false}
            showColumnFilter={false}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: handleBulkDeleteFolders,
                className: 'text-red-500',
                condition: selectedFolders.length > 0,
              },
            ]}
          />
        </div>

        <Table
          data={currentFolders}
          columns={folderColumns}
          selectable
          rowSelection={{
            selectedRowKeys: selectedFolders,
            onChange: (keys) => setSelectedFolders(keys as number[]),
          }}
          rowKey="id"

        />

        {/* Files section - if in a folder */}
        {currentFolderId && (
          <div className="mt-6">
            <div className="mb-4">
              <Typography variant="h6" className="mb-4">
                {t('business:file.title')}
              </Typography>
              <MenuIconBar
                onSearch={handleSearch}
                onAdd={() => document.getElementById('file-upload')?.click()}
                items={[]}
                showDateFilter={false}
                showColumnFilter={false}
                additionalIcons={[
                  {
                    icon: 'trash',
                    tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                    variant: 'primary',
                    onClick: handleBulkDeleteFiles,
                    className: 'text-red-500',
                    condition: selectedFiles.length > 0,
                  },
                ]}
              />
              <input
                id="file-upload"
                type="file"
                multiple
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />
            </div>

            <FileTable
              files={currentFiles}
              selectedItems={selectedFiles}
              onSelectionChange={setSelectedFiles}
              onDelete={handleDeleteFile}
              onView={handleViewFile}
            />
          </div>
        )}
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDelete}
        title={
          deleteType === 'folder'
            ? t('business:folder.deleteConfirmTitle')
            : t('business:file.deleteConfirmTitle')
        }
        message={
          deleteType === 'folder'
            ? t('business:folder.deleteConfirmMessage', { count: selectedFolders.length })
            : t('business:file.deleteConfirmMessage', { count: selectedFiles.length })
        }
      />

      {/* Create Folder Modal */}
      <Modal
        isOpen={createFolderModalOpen}
        onClose={() => {
          setCreateFolderModalOpen(false);
          setNewFolderName('');
        }}
        title={t('business:folder.createTitle', 'Tạo thư mục mới')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setCreateFolderModalOpen(false);
                setNewFolderName('');
              }}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateFolder}
              disabled={!newFolderName.trim()}
            >
              {t('business:folder.create', 'Tạo thư mục')}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('business:folder.name', 'Tên thư mục')}
            </label>
            <input
              type="text"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder={t('business:folder.namePlaceholder', 'Nhập tên thư mục')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCreateFolder();
                }
              }}
              autoFocus
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default VirtualWarehouseDetailPage;
