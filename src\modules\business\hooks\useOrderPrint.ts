import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { BusinessApiService, ShippingStatusEnum } from '../services/business-api.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Interface cho tùy chọn in đơn hàng
 */
export interface PrintOrderOptions {
  paperSize?: 'A4' | 'A5' | 'A6';
  orientation?: 'portrait' | 'landscape';
  ghnFormat?: 'A5' | '80x80' | '52x70';
  includeBarcode?: boolean;
  includeQRCode?: boolean;
}

/**
 * Interface cho kết quả API print order
 */
interface PrintOrderApiResult {
  carrier?: string;
  trackingNumber?: string;
  printType?: string;
  token?: string;
  printUrl?: string;
  url?: string;
  pdfUrl?: string;
  instructions?: string;
  format?: string;
}

/**
 * Interface cho kết quả in đơn hàng
 */
export interface PrintOrderResult {
  orderId: string;
  carrier: string;
  trackingNumber: string;
  printType: 'token' | 'pdf' | 'url';
  printData: {
    token?: string;
    printUrl?: string;
    pdfUrl?: string;
    instructions?: string;
    format?: string;
  };
  createdAt: number;
}

/**
 * Hook in đơn hàng
 */
export const useOrderPrint = () => {
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: async ({
      orderId,
      options = {}
    }: {
      orderId: number;
      options?: PrintOrderOptions
    }): Promise<PrintOrderResult> => {
      try {
        console.log('🖨️ [useOrderPrint] Printing order:', orderId, 'with options:', options);

        // Gọi API in đơn hàng từ BusinessApiService
        const result = await BusinessApiService.printOrder(orderId, options as Record<string, unknown>);

        console.log('✅ [useOrderPrint] Print result:', result);

        // Transform result to expected format
        const apiResult = result as PrintOrderApiResult;
        const printResult: PrintOrderResult = {
          orderId: String(orderId),
          carrier: apiResult?.carrier || 'unknown',
          trackingNumber: apiResult?.trackingNumber || '',
          printType: (apiResult?.printType || 'url') as 'token' | 'pdf' | 'url',
          printData: {
            ...(apiResult?.token && { token: apiResult.token }),
            ...((apiResult?.printUrl || apiResult?.url) && { printUrl: apiResult.printUrl || apiResult.url }),
            ...(apiResult?.pdfUrl && { pdfUrl: apiResult.pdfUrl }),
            ...(apiResult?.instructions && { instructions: apiResult.instructions }),
            ...(apiResult?.format && { format: apiResult.format }),
          },
          createdAt: Date.now(),
        };

        return printResult;
      } catch (error: unknown) {
        console.error('❌ [useOrderPrint] Print error:', error);
        throw error;
      }
    },
    onSuccess: (result) => {
      NotificationUtil.success({
        title: t('business:order.printSuccess', 'In đơn hàng thành công'),
        message: t('business:order.printSuccessDescription', 'Đơn hàng đã được chuẩn bị để in'),
        duration: 5000,
      });

      // Nếu có URL in, mở trong tab mới
      if (result.printData.printUrl) {
        window.open(result.printData.printUrl, '_blank');
      }
    },
    onError: (error: unknown) => {
      console.error('❌ [useOrderPrint] Print failed:', error);

      const axiosError = error as AxiosError<{ message: string }>;
      NotificationUtil.error({
        title: t('business:order.printError', 'Lỗi in đơn hàng'),
        message: axiosError.response?.data?.message || t('business:order.printErrorDescription', 'Không thể in đơn hàng. Vui lòng thử lại.'),
        duration: 5000,
      });
    },
  });
};

/**
 * Hook lấy danh sách đơn hàng có thể in
 */
export const usePrintableOrders = (params?: {
  hasShipping?: boolean;
  shippingStatus?: ShippingStatusEnum;
}) => {
  return useQuery({
    queryKey: ['business', 'orders', 'printable', params],
    queryFn: async () => {
      // Lấy danh sách đơn hàng có thể in (có tracking number)
      const result = await BusinessApiService.getOrders({
        ...params,
        // Chỉ lấy đơn hàng có vận chuyển
        // hasShipping: true,
      });

      // Filter các đơn hàng có thể in (có carrier và shipping info)
      const printableOrders = result.items.filter(order => {
        // Kiểm tra có thông tin vận chuyển trong billInfo
        const hasCarrier = order.billInfo?.selectedCarrier;
        const hasShippingService = order.billInfo?.shippingServiceType;
        // Chỉ lấy đơn hàng có thông tin vận chuyển
        return hasCarrier && hasShippingService;
      });

      return {
        ...result,
        items: printableOrders,
      };
    },
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });
};

/**
 * Hook in hàng loạt đơn hàng
 */
export const useBatchOrderPrint = () => {
  const { t } = useTranslation(['business', 'common']);

  return useMutation({
    mutationFn: async ({ 
      orderIds, 
      options = {} 
    }: { 
      orderIds: number[]; 
      options?: PrintOrderOptions 
    }): Promise<PrintOrderResult[]> => {
      try {
        console.log('🖨️ [useBatchOrderPrint] Printing orders:', orderIds, 'with options:', options);

        // In từng đơn hàng một cách tuần tự
        const results: PrintOrderResult[] = [];

        for (const orderId of orderIds) {
          try {
            const result = await BusinessApiService.printOrder(orderId, options as Record<string, unknown>);

            // Transform result to expected format
            const apiResult = result as PrintOrderApiResult;
            const printResult: PrintOrderResult = {
              orderId: String(orderId),
              carrier: apiResult?.carrier || 'unknown',
              trackingNumber: apiResult?.trackingNumber || '',
              printType: (apiResult?.printType || 'url') as 'token' | 'pdf' | 'url',
              printData: {
                ...(apiResult?.token && { token: apiResult.token }),
                ...((apiResult?.printUrl || apiResult?.url) && { printUrl: apiResult.printUrl || apiResult.url }),
                ...(apiResult?.pdfUrl && { pdfUrl: apiResult.pdfUrl }),
                ...(apiResult?.instructions && { instructions: apiResult.instructions }),
                ...(apiResult?.format && { format: apiResult.format }),
              },
              createdAt: Date.now(),
            };

            results.push(printResult);
          } catch (error) {
            console.error(`❌ [useBatchOrderPrint] Failed to print order ${orderId}:`, error);
            // Tiếp tục in các đơn hàng khác
          }
        }

        console.log('✅ [useBatchOrderPrint] Batch print results:', results);

        return results;
      } catch (error: unknown) {
        console.error('❌ [useBatchOrderPrint] Batch print error:', error);
        throw error;
      }
    },
    onSuccess: (results) => {
      const successCount = results.length;

      NotificationUtil.success({
        title: t('business:order.batchPrintSuccess', 'In hàng loạt thành công'),
        message: t('business:order.batchPrintSuccessDescription', `Đã in thành công ${successCount} đơn hàng`, { count: successCount }),
        duration: 5000,
      });

      // Mở tất cả URL in trong tab mới
      results.forEach(result => {
        if (result.printData.printUrl) {
          window.open(result.printData.printUrl, '_blank');
        }
      });
    },
    onError: (error: unknown) => {
      console.error('❌ [useBatchOrderPrint] Batch print error:', error);

      const axiosError = error as AxiosError<{ message: string }>;
      NotificationUtil.error({
        title: t('business:order.batchPrintError', 'Lỗi in hàng loạt'),
        message: axiosError.response?.data?.message || t('business:order.batchPrintErrorDescription', 'Không thể in hàng loạt đơn hàng. Vui lòng thử lại.'),
        duration: 5000,
      });
    },
  });
};

/**
 * Hook kiểm tra đơn hàng có thể in không
 */
export const useCanPrintOrder = (orderId: number) => {
  return useQuery({
    queryKey: ['business', 'orders', orderId, 'can-print'],
    queryFn: async () => {
      try {
        // Lấy chi tiết đơn hàng
        const order = await BusinessApiService.getOrderDetail(orderId);
        
        // Kiểm tra điều kiện có thể in
        const logisticInfo = order.logisticInfo as { trackingNumber?: string; shippingProvider?: string } | null;
        const canPrint = !!(
          order.hasShipping &&
          logisticInfo?.trackingNumber &&
          logisticInfo?.shippingProvider
        );

        return {
          canPrint,
          reason: canPrint ? null : 'Đơn hàng chưa có thông tin vận chuyển',
          carrier: logisticInfo?.shippingProvider || null,
          trackingNumber: logisticInfo?.trackingNumber || null,
        };
      } catch {
        return {
          canPrint: false,
          reason: 'Không thể kiểm tra thông tin đơn hàng',
          carrier: null,
          trackingNumber: null,
        };
      }
    },
    enabled: !!orderId,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });
};

/**
 * Hook lấy lịch sử in đơn hàng
 */
export const useOrderPrintHistory = (orderId: number) => {
  return useQuery({
    queryKey: ['business', 'orders', orderId, 'print-history'],
    queryFn: async () => {
      // TODO: Implement API to get print history
      // For now, return empty array
      return [];
    },
    enabled: !!orderId,
    refetchOnWindowFocus: false,
    staleTime: 300000, // 5 minutes
  });
};
