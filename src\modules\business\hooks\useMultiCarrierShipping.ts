import { useState, useCallback, useEffect } from 'react';
import { OrderItemDto } from '../types/order.types';
import { ProductTypeEnum } from '../types/product.types';
import { DeliveryAddressDto, ShippingCalculatorService } from '../services/shipping-calculator.service';

interface CarrierQuote {
  carrier: 'GHN' | 'GHTK';
  fee: number;
  serviceType: string;
  estimatedDeliveryTime?: string;
  isAvailable: boolean;
  error?: string;
}

interface UseMultiCarrierShippingProps {
  shopId: number;
  customerId?: number;
  selectedItems: OrderItemDto[];
  deliveryAddress?: DeliveryAddressDto;
  autoCalculate?: boolean;
}

interface UseMultiCarrierShippingReturn {
  quotes: CarrierQuote[];
  selectedCarrier: CarrierQuote | null;
  isCalculating: boolean;
  calculationError: string;
  hasPhysicalProducts: boolean;
  calculateShipping: () => Promise<void>;
  selectCarrier: (carrier: 'GHN' | 'GHTK') => void;
  retryCalculation: () => Promise<void>;
}

/**
 * Hook quản lý tính phí vận chuyển từ nhiều nhà vận chuyển
 */
export const useMultiCarrierShipping = ({
  shopId,
  customerId,
  selectedItems,
  deliveryAddress,
  autoCalculate = true,
}: UseMultiCarrierShippingProps): UseMultiCarrierShippingReturn => {
  // State
  const [quotes, setQuotes] = useState<CarrierQuote[]>([]);
  const [selectedCarrier, setSelectedCarrier] = useState<CarrierQuote | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [calculationError, setCalculationError] = useState<string>('');

  // Kiểm tra có sản phẩm cần vận chuyển không
  const hasPhysicalProducts = selectedItems.some(() => {
    // TODO: Cần implement logic kiểm tra productType từ item
    // Hiện tại giả sử tất cả đều là PHYSICAL
    // Trong thực tế cần fetch thông tin sản phẩm hoặc có productType trong OrderItemDto
    return true;
  });

  // Tính phí vận chuyển từ tất cả carriers
  const calculateShipping = useCallback(async () => {
    if (!deliveryAddress || !hasPhysicalProducts || selectedItems.length === 0) {
      setQuotes([]);
      setSelectedCarrier(null);
      return;
    }

    setIsCalculating(true);
    setCalculationError('');

    const carriers: ('GHN' | 'GHTK')[] = ['GHN', 'GHTK'];
    const newQuotes: CarrierQuote[] = [];

    try {
      console.log('🚚 [useMultiCarrierShipping] Starting calculation for carriers:', carriers);
      console.log('🚚 [useMultiCarrierShipping] Shop ID:', shopId);
      console.log('🚚 [useMultiCarrierShipping] Selected items:', selectedItems);
      console.log('🚚 [useMultiCarrierShipping] Delivery address:', deliveryAddress);

      // Tính phí song song cho tất cả carriers
      const results = await Promise.allSettled(
        carriers.map(async (carrier) => {
          try {
            console.log(`🚚 [useMultiCarrierShipping] Calculating for ${carrier}...`);

            // Đảm bảo dữ liệu được normalize trước khi gửi request
            const normalizedShopId = typeof shopId === 'string' ? parseInt(shopId, 10) : shopId;
            const normalizedCustomerId = customerId !== undefined && customerId !== null
              ? (typeof customerId === 'string' ? parseInt(customerId, 10) : customerId)
              : undefined;

            const request = ShippingCalculatorService.createCalculateShippingRequest(
              normalizedShopId,
              selectedItems.map(item => ({
                productId: typeof item.productId === 'string' ? parseInt(item.productId, 10) : item.productId,
                quantity: typeof item.quantity === 'string' ? parseInt(item.quantity, 10) : item.quantity,
              })),
              normalizedCustomerId,
              deliveryAddress,
              carrier
            );

            console.log(`🚚 [useMultiCarrierShipping] ${carrier} request:`, request);

            const response = await ShippingCalculatorService.calculateShippingFee(request);

            console.log(`✅ [useMultiCarrierShipping] ${carrier} response:`, response);

            // Kiểm tra response có hợp lệ không
            if (response.code === 0 && response.result) {
              return {
                carrier,
                fee: response.result.fee || 0,
                serviceType: response.result.serviceType || 'Chuẩn',
                estimatedDeliveryTime: response.result.estimatedDeliveryTime,
                isAvailable: true,
              } as CarrierQuote;
            } else {
              // Nếu API trả về lỗi, throw error để catch block xử lý
              throw new Error(response.message || `${carrier} calculation failed`);
            }
          } catch (error) {
            console.error(`❌ [useMultiCarrierShipping] Error calculating ${carrier} shipping:`, error);

            // Log chi tiết hơn về lỗi
            if (error instanceof Error) {
              console.error(`❌ [useMultiCarrierShipping] ${carrier} error details:`, {
                message: error.message,
                stack: error.stack,
                name: error.name
              });
            }

            return {
              carrier,
              fee: 0,
              serviceType: '',
              isAvailable: false,
              error: error instanceof Error ? error.message : 'Calculation failed',
            } as CarrierQuote;
          }
        })
      );

      // Xử lý kết quả
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          newQuotes.push(result.value);
        } else {
          const carrier = carriers[index];
          console.error(`❌ [useMultiCarrierShipping] ${carrier} calculation rejected:`, result.reason);
          if (carrier) {
            newQuotes.push({
              carrier,
              fee: 0,
              serviceType: '',
              isAvailable: false,
              error: result.reason?.message || 'Unknown error',
            });
          }
        }
      });

      console.log('🚚 [useMultiCarrierShipping] Final quotes:', newQuotes);

      setQuotes(newQuotes);

      // Tự động chọn carrier rẻ nhất nếu có
      const availableQuotes = newQuotes.filter(q => q.isAvailable);
      if (availableQuotes.length > 0) {
        const cheapest = availableQuotes.reduce((prev, current) => 
          prev.fee < current.fee ? prev : current
        );
        console.log('🚚 [useMultiCarrierShipping] Auto-selecting cheapest carrier:', cheapest);
        setSelectedCarrier(cheapest);
      } else {
        setSelectedCarrier(null);
      }

    } catch (error) {
      console.error('❌ [useMultiCarrierShipping] Error in multi-carrier calculation:', error);
      setCalculationError(error instanceof Error ? error.message : 'Calculation failed');
      setQuotes([]);
      setSelectedCarrier(null);
    } finally {
      setIsCalculating(false);
    }
  }, [shopId, customerId, selectedItems, deliveryAddress, hasPhysicalProducts]);

  // Chọn carrier thủ công
  const selectCarrier = useCallback((carrier: 'GHN' | 'GHTK') => {
    const quote = quotes.find(q => q.carrier === carrier && q.isAvailable);
    if (quote) {
      console.log('🚚 [useMultiCarrierShipping] Manually selecting carrier:', quote);
      setSelectedCarrier(quote);
    }
  }, [quotes]);

  // Retry calculation
  const retryCalculation = useCallback(async () => {
    console.log('🔄 [useMultiCarrierShipping] Retrying calculation...');
    await calculateShipping();
  }, [calculateShipping]);

  // Auto-calculate khi có thay đổi
  useEffect(() => {
    if (autoCalculate && deliveryAddress && hasPhysicalProducts && selectedItems.length > 0) {
      console.log('🔄 [useMultiCarrierShipping] Auto-calculating due to dependency change...');
      calculateShipping();
    }
  }, [autoCalculate, calculateShipping, deliveryAddress, hasPhysicalProducts, selectedItems.length]);

  // Reset khi không có sản phẩm cần vận chuyển
  useEffect(() => {
    if (!hasPhysicalProducts) {
      console.log('🚚 [useMultiCarrierShipping] No physical products, resetting state...');
      setQuotes([]);
      setSelectedCarrier(null);
      setCalculationError('');
    }
  }, [hasPhysicalProducts]);

  return {
    quotes,
    selectedCarrier,
    isCalculating,
    calculationError,
    hasPhysicalProducts,
    calculateShipping,
    selectCarrier,
    retryCalculation,
  };
};

/**
 * Helper function để kiểm tra sản phẩm có cần vận chuyển không
 */
export const needsShipping = (productType: ProductTypeEnum): boolean => {
  switch (productType) {
    case ProductTypeEnum.PHYSICAL:
      return true;
    case ProductTypeEnum.COMBO:
      // COMBO cần vận chuyển nếu chứa sản phẩm PHYSICAL
      // TODO: Implement logic kiểm tra sản phẩm con trong combo
      return true;
    case ProductTypeEnum.DIGITAL:
    case ProductTypeEnum.EVENT:
    case ProductTypeEnum.SERVICE:
      return false;
    default:
      return false;
  }
};

/**
 * Helper function để tính tổng trọng lượng và kích thước cho vận chuyển
 */
export const calculateShippingDimensions = (items: OrderItemDto[]) => {
  // TODO: Implement logic tính toán dựa trên shipmentConfig của từng sản phẩm
  console.log('Calculating shipping dimensions for items:', items.length);
  return {
    totalWeight: 0,
    totalVolume: 0,
    dimensions: {
      length: 0,
      width: 0,
      height: 0,
    },
  };
};

export default useMultiCarrierShipping;
