import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Input,
  Loading,
  Typography,
  Icon,
} from '@/shared/components/common';
import { useCustomGroupFormSearch, CustomGroupFormSearchItem } from '../hooks/useCustomGroupFormSearch';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

export interface CustomGroupFormData {
  id: number;
  label: string;
  productId: number | null;
  createAt: number;
  fieldCount?: number | undefined;
}

interface CustomGroupFormSelectorProps {
  onGroupSelect: (group: CustomGroupFormData) => void;
  placeholder?: string;
  className?: string;
}

/**
 * Component để search và chọn nhóm trường tùy chỉnh
 */
const CustomGroupFormSelector: React.FC<CustomGroupFormSelectorProps> = ({
  onGroupSelect,
  placeholder = 'Tì<PERSON> kiếm nhóm trường tùy chỉnh...',
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [, setSelectedGroup] = useState<CustomGroupFormData | null>(null);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    items,
    isLoading,
    hasMore,
    error,
    searchCustomGroupForms,
    loadMore,
    clearSearch,
  } = useCustomGroupFormSearch();

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    if (value.trim()) {
      searchCustomGroupForms(value.trim());
      setIsDropdownOpen(true);
    } else {
      clearSearch();
      setIsDropdownOpen(false);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (searchTerm.trim()) {
      setIsDropdownOpen(true);
    } else {
      // Load initial data when focus without search term
      searchCustomGroupForms('');
      setIsDropdownOpen(true);
    }
  };

  // Handle item selection
  const handleItemSelect = (item: CustomGroupFormSearchItem) => {
    const groupData: CustomGroupFormData = {
      id: item.id,
      label: item.label,
      productId: item.productId,
      createAt: item.createAt,
      fieldCount: item.fieldCount ?? undefined,
    };

    setSelectedGroup(groupData);
    setSearchTerm(item.label);
    setIsDropdownOpen(false);
    onGroupSelect(groupData);
  };

  // Handle clear selection
  const handleClear = () => {
    setSelectedGroup(null);
    setSearchTerm('');
    setIsDropdownOpen(false);
    clearSearch();
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Format date with validation
  const formatDate = (timestamp: number) => {
    try {
      // Validate timestamp
      if (!timestamp || isNaN(timestamp) || timestamp <= 0) {
        return 'N/A';
      }

      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      return format(date, 'dd/MM/yyyy', { locale: vi });
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'N/A';
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Search Input */}
      <div className="relative">
        <Input
          ref={inputRef}
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          fullWidth
          className="pr-10"
        />

        {/* Clear button */}
        {searchTerm && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <Icon name="x" size="sm" />
          </button>
        )}
      </div>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-64 overflow-y-auto">
          {/* Loading */}
          {isLoading && items.length === 0 && (
            <div className="flex items-center justify-center p-4">
              <Loading size="sm" className="mr-2" />
              <Typography variant="caption">
                {t('common:loading', 'Đang tải...')}
              </Typography>
            </div>
          )}

          {/* Error */}
          {error && (
            <div className="p-4 text-center">
              <Typography variant="caption" className="text-red-500">
                {error}
              </Typography>
            </div>
          )}

          {/* No results */}
          {!isLoading && !error && items.length === 0 && searchTerm && (
            <div className="p-4 text-center">
              <Typography variant="caption" className="text-gray-500">
                {t('business:customGroupForm.noResults', 'Không tìm thấy nhóm trường tùy chỉnh nào')}
              </Typography>
            </div>
          )}

          {/* Results */}
          {items.map((item) => (
            <div
              key={item.id}
              onClick={() => handleItemSelect(item)}
              className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <Typography variant="body2" className="font-medium truncate">
                    {item.label}
                  </Typography>

                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                      ID: {item.id}
                    </span>
                    {item.fieldCount !== undefined && (
                      <span className="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded">
                        {item.fieldCount} trường
                      </span>
                    )}
                    <span className="text-xs text-gray-500">
                      {formatDate(item.createAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Load more */}
          {hasMore && !isLoading && items.length > 0 && (
            <div className="p-2 border-t border-gray-100 dark:border-gray-600">
              <button
                type="button"
                onClick={loadMore}
                className="w-full p-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded"
              >
                {t('common:loadMore', 'Tải thêm')}
              </button>
            </div>
          )}

          {/* Loading more */}
          {isLoading && items.length > 0 && (
            <div className="flex items-center justify-center p-2 border-t border-gray-100 dark:border-gray-600">
              <Loading size="sm" className="mr-2" />
              <Typography variant="caption">
                {t('common:loading', 'Đang tải...')}
              </Typography>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CustomGroupFormSelector;
