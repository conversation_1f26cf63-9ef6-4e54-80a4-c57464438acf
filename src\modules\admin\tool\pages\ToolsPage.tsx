/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Modal,
  Typography,
  Button,
  Pagination,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ToolGrid, ToolForm } from '../components';
import {
  useAdminTools,
  useCreateAdminTool,
  useDeleteAdminTool,
  useAdminToolDetail,
  useUpdateAdminTool
} from '../hooks';
import { useUpdateAdminToolVersion, useDeleteAdminToolVersion, useCreateAdminToolVersion } from '../hooks/useToolVersion';
import { ToolListItem, ToolStatus, ToolSortBy, CreateToolParams, UpdateToolParams, UpdateToolVersionParams, CreateToolVersionParams } from '../types/tool.types';

/**
 * Trang hiển thị danh sách tools dạng grid
 */
const ToolsPage: React.FC = () => {
  const { t } = useTranslation(['admin']);


  // State cho tìm kiếm và lọc
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ToolStatus | 'all'>('all');

  // State cho phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  // State cho xóa tool
  const [toolToDelete, setToolToDelete] = useState<ToolListItem | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State cho xem/chỉnh sửa tool
  const [toolToView, setToolToView] = useState<ToolListItem | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem/chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: itemsPerPage,
      sortBy: ToolSortBy.CREATED_AT,
      sortDirection: 'DESC' as const,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (filterStatus !== 'all') {
      params.status = filterStatus;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, filterStatus]);

  // Hooks để gọi API
  const { data: toolsData, isLoading } = useAdminTools(queryParams);
  const createToolMutation = useCreateAdminTool();
  const deleteToolMutation = useDeleteAdminTool();
  const updateToolMutation = useUpdateAdminTool();
  const updateVersionMutation = useUpdateAdminToolVersion();
  const deleteVersionMutation = useDeleteAdminToolVersion();
  const createVersionMutation = useCreateAdminToolVersion();
  const { data: toolDetail, isLoading: isLoadingDetail } = useAdminToolDetail(
    toolToView?.id || ''
  );

  const createTool = createToolMutation.mutateAsync;
  const isCreating = createToolMutation.isPending || false;
  const deleteTool = deleteToolMutation.mutateAsync;
  const isDeleting = deleteToolMutation.isPending || false;
  const updateTool = updateToolMutation.mutateAsync;
  const isUpdating = updateToolMutation.isPending || false;
  const updateVersion = updateVersionMutation.mutateAsync;
  const isUpdatingVersion = updateVersionMutation.isPending || false;
  const deleteVersion = deleteVersionMutation.mutateAsync;
  const isDeletingVersion = deleteVersionMutation.isPending || false;
  const createVersion = createVersionMutation.mutateAsync;
  const isCreatingVersion = createVersionMutation.isPending || false;

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = useCallback((status: string) => {
    setFilterStatus(status as ToolStatus | 'all');
    setCurrentPage(1); // Reset về trang 1 khi lọc
  }, []);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Xử lý thay đổi số mục trên trang
  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
  }, []);

  // Xử lý xem chi tiết tool
  const handleViewTool = useCallback((tool: ToolListItem) => {
    setToolToView(tool);
    setIsEditMode(false);
    showEditForm();
  }, [showEditForm]);

  // Xử lý chỉnh sửa tool
  const handleEditTool = useCallback((tool: ToolListItem) => {
    setToolToView(tool);
    setIsEditMode(true);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setToolToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!toolToDelete) return;

    try {
      await deleteTool(toolToDelete.id);
      setShowDeleteConfirm(false);
      setToolToDelete(null);
    } catch (error) {
      console.error('Error deleting tool:', error);
    }
  }, [toolToDelete, deleteTool]);

  // Xử lý submit form tạo tool
  const handleSubmitCreateTool = useCallback((values: CreateToolParams | UpdateToolParams) => {
    try {
      if ('toolName' in values) {
        // Nếu là CreateToolParams
        createTool(values).then(() => {
          hideCreateForm();
        }).catch((error) => {
          console.error('Error creating tool:', error);
        });
      }
    } catch (error) {
      console.error('Error creating tool:', error);
    }
  }, [createTool, hideCreateForm]);

  // Xử lý submit form chỉnh sửa tool
  const handleSubmitEditTool = useCallback((values: CreateToolParams | UpdateToolParams) => {
    if (!toolToView) return;

    try {
      updateTool({ id: toolToView.id, data: values as UpdateToolParams }).then(() => {
        hideEditForm();
        setToolToView(null);
      }).catch((error) => {
        console.error('Error updating tool:', error);
      });
    } catch (error) {
      console.error('Error updating tool:', error);
    }
  }, [updateTool, hideEditForm, toolToView]);

  // Xử lý submit form chỉnh sửa version
  const handleSubmitEditVersion = useCallback((versionId: string, values: UpdateToolVersionParams) => {
    if (!toolToView) return;

    try {
      updateVersion({
        toolId: toolToView.id,
        versionId: versionId,
        data: values
      }).then(() => {
        // Version updated successfully
      }).catch((error) => {
        console.error('Error updating version:', error);
      });
    } catch (error) {
      console.error('Error updating version:', error);
    }
  }, [updateVersion, toolToView]);

  // Xử lý submit form xóa version
  const handleSubmitDeleteVersion = useCallback((versionId: string) => {
    if (!toolToView) return;

    try {
      deleteVersion({
        toolId: toolToView.id,
        versionId: versionId
      }).then(() => {
        // Version deleted successfully
      }).catch((error) => {
        console.error('Error deleting version:', error);
      });
    } catch (error) {
      console.error('Error deleting version:', error);
    }
  }, [deleteVersion, toolToView]);

  // Xử lý submit form tạo version mới
  const handleSubmitCreateVersion = useCallback((values: CreateToolVersionParams) => {
    if (!toolToView) return;

    try {
      createVersion({
        toolId: toolToView.id,
        data: values
      }).then(() => {
        // Version created successfully
      }).catch((error) => {
        console.error('Error creating version:', error);
      });
    } catch (error) {
      console.error('Error creating version:', error);
    }
  }, [createVersion, toolToView]);

  // Xử lý đóng form xem/chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setToolToView(null);
    setIsEditMode(false);
  }, [hideEditForm]);

  // Xử lý xóa tool
  const handleDeleteTool = useCallback((tool: ToolListItem) => {
    setToolToDelete(tool);
    setShowDeleteConfirm(true);
  }, []);

  return (
    <div className="space-y-4">
      {/* Menu bar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={showCreateForm}
        items={[
          {
            id: 'all',
            label: t('admin:common.all', 'All'),
            icon: 'list',
            onClick: () => handleFilterStatus('all'),
          },
          {
            id: 'draft',
            label: t('admin:tool.status.draft', 'Draft'),
            icon: 'file',
            onClick: () => handleFilterStatus(ToolStatus.DRAFT),
          },
          {
            id: 'approved',
            label: t('admin:tool.status.approved', 'Approved'),
            icon: 'check-circle',
            onClick: () => handleFilterStatus(ToolStatus.APPROVED),
          },
          {
            id: 'deprecated',
            label: t('admin:tool.status.deprecated', 'Deprecated'),
            icon: 'archive',
            onClick: () => handleFilterStatus(ToolStatus.DEPRECATED),
          },
        ]}
      />

      {/* SlideInForm cho form thêm mới */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <ToolForm
          onSubmit={handleSubmitCreateTool}
          onCancel={hideCreateForm}
          isLoading={isCreating}
        />
      </SlideInForm>

      {/* SlideInForm cho form xem/chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {toolToView && (
          <ToolForm
            initialValues={toolDetail || (toolToView as any)}
            onSubmit={handleSubmitEditTool}
            onSubmitVersion={handleSubmitEditVersion}
            onCreateVersion={handleSubmitCreateVersion}
            onDeleteVersion={handleSubmitDeleteVersion}
            onCancel={handleCloseEditForm}
            isLoading={isUpdating || isLoadingDetail || isUpdatingVersion || isDeletingVersion || isCreatingVersion}
            isEdit={isEditMode}
            readOnly={!isEditMode}
          />
        )}
      </SlideInForm>

      {/* Hiển thị danh sách tool */}
      {toolsData?.items && toolsData.items.length > 0 ? (
        <>
          <ToolGrid
            tools={toolsData.items}
            onViewTool={handleViewTool}
            onEditTool={handleEditTool}
            onDeleteTool={handleDeleteTool}
          />

          {/* Phân trang */}
          {toolsData.meta && toolsData.meta.totalItems > 0 && (
            <div className="mt-6">
              <Pagination
                variant="default"
                currentPage={currentPage}
                totalItems={toolsData.meta.totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                itemsPerPageOptions={[12, 24, 48, 96]}
                showFirstLastButtons={true}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                size="md"
              />
            </div>
          )}
        </>
      ) : (
        <Card className="p-8">
          <div className="flex flex-col items-center justify-center">
            <Typography variant="h6" className="text-gray-500 dark:text-gray-400">
              {isLoading
                ? t('admin:tool.loading', 'Loading...')
                : t('admin:tool.noTools', 'No tools found')}
            </Typography>
          </div>
        </Card>
      )}

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('admin:tool.confirmDelete', 'Confirm Delete')}
        size="sm"
        footer={
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('admin:tool.cancel', 'Cancel')}
            </Button>
            <Button variant="danger" onClick={handleConfirmDelete} disabled={isDeleting}>
              {isDeleting ? t('admin:tool.deleting', 'Deleting...') : t('admin:tool.delete', 'Delete')}
            </Button>
          </div>
        }
      >
        <div className="py-4">
          <Typography>
            {t('admin:tool.confirmDeleteMessage', 'Are you sure you want to delete this tool?')}
          </Typography>
          {toolToDelete && (
            <Typography variant="body2" className="mt-2 font-semibold">
              {toolToDelete.name}
            </Typography>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default ToolsPage;


