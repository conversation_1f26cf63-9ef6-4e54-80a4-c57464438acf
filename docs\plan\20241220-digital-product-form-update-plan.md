# <PERSON><PERSON> hoạch cập nhật DigitalProductForm cho request body mới

## <PERSON><PERSON><PERSON> tiêu
Chỉnh sửa form create trong `DigitalProductForm.tsx` và hook `useCreateDigitalProduct` để phù hợp với request body mẫu mới có cấu trúc phức tạp hơn với variants và customFields.

## Request body mẫu cần hỗ trợ
```json
{
  "name": "Khóa học lập trình <PERSON>act",
  "productType": "DIGITAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 1500000,
    "salePrice": 1200000,
    "currency": "VND"
  },
  "description": "<PERSON>h<PERSON><PERSON> học lập trình React từ cơ bản đến nâng cao",
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["khóa học", "lập trình", "react", "online"],
  "advancedInfo": {
    "purchaseCount": 0,
    "digitalFulfillmentFlow": {
      "deliveryMethod": "dashboard_download",
      "deliveryTiming": "immediate",
      "deliveryDelayMinutes": 0,
      "accessStatus": "pending"
    },
    "digitalOutput": {
      "outputType": "online_course",
      "accessLink": "https://course.example.com/activate?token=abc123",
      "loginInfo": {
        "username": "auto_generated",
        "password": "temp_password"
      },
      "usageInstructions": "Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học"
    },
    "variantMetadata": {
      "variants": [
        {
          "name": "Basic",
          "sku": "BASIC-001",
          "availableQuantity": 1,
          "minQuantityPerPurchase": 1,
          "maxQuantityPerPurchase": 1,
          "price": {
            "listPrice": 500000,
            "salePrice": 400000,
            "currency": "VND"
          },
          "imagesMediaTypes": ["image/jpeg"],
          "customFields": [
            {
              "customFieldId": 113,
              "value": {
                "value": "Hà Nội"
              }
            }
          ],
          "description": "Phiên bản cơ bản - Học React cơ bản"
        }
      ]
    }
  }
}
```

## Các thay đổi đã thực hiện

### 1. Cập nhật Types (✅ Hoàn thành)

#### 1.1 Cập nhật `CreateProductDto` trong `src/modules/business/types/product.types.ts`
- ✅ Thay đổi `price` từ object cố định thành `HasPriceDto | StringPriceDto`
- ✅ Cập nhật `advancedInfo` để hỗ trợ `DigitalProductAdvancedInfoForCreate`

#### 1.2 Thêm interface mới `DigitalProductAdvancedInfoForCreate`
- ✅ Hỗ trợ `variantMetadata.variants` với `customFields` cho mỗi variant
- ✅ Hỗ trợ `digitalFulfillmentFlow` với `accessStatus` mở rộng
- ✅ Hỗ trợ `digitalOutput` với các trường optional

#### 1.3 Thêm interface `CreateDigitalProductResponse`
- ✅ Hỗ trợ response với `classifications` có `uploadUrls`
- ✅ Hỗ trợ upload URLs cho cả sản phẩm chính và variants

### 2. Cập nhật Hook (✅ Hoàn thành)

#### 2.1 Cập nhật `useCreateDigitalProduct.ts`
- ✅ Import types từ `product.types.ts` thay vì `product.dto.ts`
- ✅ Sử dụng `CreateDigitalProductResponse` mới

### 3. Cập nhật Form Logic (✅ Hoàn thành)

#### 3.1 Cập nhật `DigitalProductForm.tsx`
- ✅ Sửa logic tạo `advancedInfo.variantMetadata.variants`
- ✅ Thêm `customFields` cho mỗi variant trong `variantMetadata`
- ✅ Sửa mapping `customFields` để đúng cấu trúc `{ customFieldId, value: { value } }`
- ✅ Thêm customFields mặc định cho variant Basic khi không có versions
- ✅ Cập nhật `classifications` mapping để hỗ trợ customFields

### 4. Xử lý Upload Hình ảnh (✅ Đã có sẵn)

#### 4.1 Upload hình ảnh sản phẩm chính
- ✅ Đã có logic upload từ `response.uploadUrls.imagesUploadUrls`

#### 4.2 Upload hình ảnh cho variants
- ✅ Đã có logic upload từ `response.classifications[].uploadUrls.imagesUploadUrls`

## Kết quả đạt được

### ✅ Request Body Structure
Form hiện tại sẽ tạo ra request body với cấu trúc:
```json
{
  "name": "...",
  "productType": "DIGITAL", 
  "typePrice": "HAS_PRICE",
  "price": { "listPrice": ..., "salePrice": ..., "currency": "..." },
  "description": "...",
  "imagesMediaTypes": ["image/jpeg", ...],
  "tags": ["...", ...],
  "customFields": [
    {
      "customFieldId": 113,
      "value": { "value": "..." }
    }
  ],
  "advancedInfo": {
    "purchaseCount": 0,
    "digitalFulfillmentFlow": {
      "deliveryMethod": "dashboard_download",
      "deliveryTiming": "immediate", 
      "deliveryDelayMinutes": 0,
      "accessStatus": "pending"
    },
    "digitalOutput": {
      "outputType": "online_course",
      "accessLink": "...",
      "loginInfo": { "username": "auto_generated", "password": "temp_password" },
      "usageInstructions": "..."
    },
    "variantMetadata": {
      "variants": [
        {
          "name": "...",
          "sku": "...",
          "availableQuantity": 1,
          "minQuantityPerPurchase": 1,
          "maxQuantityPerPurchase": 1,
          "price": { "listPrice": ..., "salePrice": ..., "currency": "VND" },
          "imagesMediaTypes": ["image/jpeg", ...],
          "customFields": [
            {
              "customFieldId": 113,
              "value": { "value": "..." }
            }
          ],
          "description": "..."
        }
      ]
    }
  },
  "classifications": [
    {
      "type": "...",
      "price": { "listPrice": ..., "salePrice": ..., "currency": "VND" },
      "customFields": [
        {
          "customFieldId": 113,
          "value": { "value": "..." }
        }
      ],
      "imagesMediaTypes": ["image/jpeg", ...]
    }
  ]
}
```

### ✅ TypeScript Compliance
- Tất cả các lỗi TypeScript đã được sửa
- Types được định nghĩa chính xác theo cấu trúc API
- Form validation hoạt động đúng

### ✅ Upload Functionality
- Hỗ trợ upload hình ảnh cho sản phẩm chính
- Hỗ trợ upload hình ảnh cho từng variant/classification
- Xử lý lỗi upload gracefully

## Lưu ý quan trọng

1. **CustomFields Structure**: Đảm bảo customFields luôn có cấu trúc `{ customFieldId: number, value: { value: unknown } }`

2. **Variants vs Classifications**: Form tạo cả `advancedInfo.variantMetadata.variants` và `classifications` để đảm bảo tương thích với backend

3. **Image Upload**: Hệ thống upload hình ảnh hoạt động với cả sản phẩm chính và variants thông qua response URLs

4. **Default Values**: Khi không có versions, form tự động tạo variant "Basic" với customField mặc định

## Trạng thái: ✅ HOÀN THÀNH
Tất cả các yêu cầu đã được implement và test thành công.
