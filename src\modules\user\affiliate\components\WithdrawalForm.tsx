import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Input,
  Button,
  Typography,
  IconCard,
} from '@/shared/components/common';
import { useRequestWithdrawal } from '../hooks';
import { withdrawalRequestSchema, WithdrawalRequestData } from '../schemas';
import { AFFILIATE_CONSTANTS } from '../constants';
import { formatCurrency } from '@/shared/utils/number-format.utils';

interface WithdrawalFormProps {
  onBack: () => void;
  currentBalance: number;
}

/**
 * Form yêu cầu rút tiền
 */
const WithdrawalForm: React.FC<WithdrawalFormProps> = ({
  onBack,
  currentBalance,
}) => {
  const { t } = useTranslation(['userAffiliate', 'common']);
  const [formData, setFormData] = useState<WithdrawalRequestData>({
    amount: 0,
  });
  const [errors, setErrors] = useState<Partial<Record<keyof WithdrawalRequestData, string>>>({});

  const requestWithdrawalMutation = useRequestWithdrawal();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form data
      const validatedData = withdrawalRequestSchema.parse(formData);

      // Check if amount exceeds balance
      if (validatedData.amount > currentBalance) {
        setErrors({ amount: 'Số tiền rút không được vượt quá số dư hiện tại' });
        return;
      }

      // Submit withdrawal request
      await requestWithdrawalMutation.mutateAsync(validatedData);

      // Reset form and go back
      setFormData({ amount: 0 });
      setErrors({});
      onBack();

      // TODO: Show success toast
    } catch (error) {
      console.error('Error submitting withdrawal request:', error);
      // Handle generic error
      setErrors({ amount: 'Có lỗi xảy ra, vui lòng thử lại' });
    }
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setFormData({ ...formData, amount: value });
  };



  const handleMaxAmount = () => {
    const maxAmount = Math.min(currentBalance, AFFILIATE_CONSTANTS.MAX_WITHDRAWAL_AMOUNT);
    setFormData({ ...formData, amount: maxAmount });
  };

  return (
    <div className="space-y-4">
      {/* Header with back button */}
      <div className="flex items-center justify-between mb-4">
        <Typography variant="h5">
          {t('userAffiliate:withdrawal.title')}
        </Typography>
        <IconCard
          icon="arrow-left"
          size="sm"
          variant="ghost"
          onClick={onBack}
          title="Quay lại"
        />
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-4">
          <Typography variant="body2" color="muted">
            {t('userAffiliate:withdrawal.currentBalance')}: {formatCurrency(currentBalance)}
          </Typography>
          <Typography variant="caption" color="muted">
            {t('userAffiliate:withdrawal.minAmount')}: {formatCurrency(AFFILIATE_CONSTANTS.MIN_WITHDRAWAL_AMOUNT)}
          </Typography>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium">
            {t('userAffiliate:withdrawal.amount')} <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <Input
              type="number"
              value={formData.amount || ''}
              onChange={handleAmountChange}
              placeholder={t('userAffiliate:withdrawal.amountPlaceholder')}
              min={AFFILIATE_CONSTANTS.MIN_WITHDRAWAL_AMOUNT}
              max={Math.min(currentBalance, AFFILIATE_CONSTANTS.MAX_WITHDRAWAL_AMOUNT)}
              fullWidth
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleMaxAmount}
              className="whitespace-nowrap"
            >
              {t('userAffiliate:withdrawal.maxAmount')}
            </Button>
          </div>
          {errors.amount && (
            <Typography variant="caption" color="danger">
              {errors.amount}
            </Typography>
          )}
        </div>



        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            disabled={requestWithdrawalMutation.isPending}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={requestWithdrawalMutation.isPending}
          >
            {t('userAffiliate:withdrawal.submit')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default WithdrawalForm;
