import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Radio,
  Alert,
} from '@/shared/components/common';
import { useAvailableCarriers, AvailableCarrier } from '../../hooks/useAvailableCarriers';

interface PreferredCarrierSelectorProps {
  selectedCarrier?: 'GHN' | 'GHTK';
  onCarrierChange: (carrier: 'GHN' | 'GHTK') => void;
  className?: string;
  disabled?: boolean;
}

/**
 * Component để chọn nhà vận chuyển ưu tiên từ danh sách providers đã cấu hình
 */
const PreferredCarrierSelector: React.FC<PreferredCarrierSelectorProps> = ({
  selectedCarrier,
  onCarrierChange,
  className = '',
  disabled = false,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // L<PERSON>y danh sách carriers có sẵn
  const { 
    carriers, 
    isLoading, 
    error, 
    hasActiveCarriers 
  } = useAvailableCarriers();

  // Xử lý chọn carrier
  const handleCarrierSelect = useCallback((carrier: AvailableCarrier) => {
    if (!disabled) {
      console.log('🚚 [PreferredCarrierSelector] Selected carrier:', carrier);
      onCarrierChange(carrier.type);
    }
  }, [onCarrierChange, disabled]);

  // Render loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
          </Typography>
          <div className="flex items-center justify-center py-8">
            <Icon name="loader" size="lg" className="animate-spin text-gray-400" />
            <Typography variant="body2" className="ml-2 text-gray-500">
              {t('business:order.loadingCarriers', 'Đang tải danh sách nhà vận chuyển...')}
            </Typography>
          </div>
        </div>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className={className}>
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
          </Typography>
          <Alert
            type="error"
            message={t('business:order.carrierLoadError', 'Lỗi tải danh sách nhà vận chuyển')}
            description={error}
          />
        </div>
      </Card>
    );
  }

  // Render empty state
  if (!hasActiveCarriers) {
    return (
      <div className={className}>
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-warning/10 rounded-full flex items-center justify-center">
            <Icon name="truck" size="lg" className="text-warning" />
          </div>
          <Typography variant="h6" className="text-foreground mb-2">
            {t('business:order.noCarriersConfigured', 'Chưa có nhà vận chuyển nào được cấu hình')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mb-6 max-w-md mx-auto">
            {t('business:order.noCarriersDescription', 'Vui lòng cấu hình ít nhất một nhà vận chuyển để sử dụng tính năng tính phí vận chuyển tự động.')}
          </Typography>
          <Button
            variant="primary"
            size="md"
            onClick={() => {
              // TODO: Navigate to shipping configuration page
              console.log('Navigate to shipping configuration');
            }}
            className="shadow-sm"
          >
            <Icon name="settings" size="sm" className="mr-2" />
            {t('business:order.configureCarriers', 'Cấu hình nhà vận chuyển')}
          </Button>
        </div>
      </div>
    );
  }

  // Render carrier selection
  return (
    <div className={className}>
      <div>
        <div className="grid grid-cols-2 gap-4">
          {carriers.map((carrier) => (
            <div
              key={carrier.id}
              className={`
                group relative border-2 rounded-xl p-4 cursor-pointer transition-all duration-200
                ${selectedCarrier === carrier.type
                  ? 'border-primary bg-primary/5 shadow-sm'
                  : 'border-border bg-card hover:border-primary/50 hover:bg-primary/2'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={() => handleCarrierSelect(carrier)}
            >
              <div className="flex items-start space-x-4">
                <div className="mt-1">
                  <Radio
                    checked={selectedCarrier === carrier.type}
                    onChange={() => handleCarrierSelect(carrier)}
                    disabled={disabled}
                    className="text-primary"
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`
                      w-12 h-12 rounded-xl flex items-center justify-center transition-colors duration-200
                      ${selectedCarrier === carrier.type
                        ? 'bg-primary/10'
                        : carrier.type === 'GHN'
                        ? 'bg-blue-50 dark:bg-blue-900/20'
                        : 'bg-green-50 dark:bg-green-900/20'
                      }
                    `}>
                      <Icon
                        name={carrier.icon}
                        size="lg"
                        className={`
                          transition-colors duration-200
                          ${selectedCarrier === carrier.type
                            ? 'text-primary'
                            : carrier.type === 'GHN'
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-green-600 dark:text-green-400'
                          }
                        `}
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <Typography variant="subtitle1" className="font-semibold text-foreground">
                          {carrier.displayName}
                        </Typography>
                      </div>
                      <Typography variant="body2" className="text-muted-foreground mt-1 leading-relaxed">
                        {carrier.description}
                      </Typography>
                    </div>
                  </div>


                </div>


              </div>
            </div>
          ))}
        </div>

        {!selectedCarrier && (
          <div className="mt-6 p-4 bg-info/10 border border-info/20 rounded-lg">
            <div className="flex items-start space-x-3">
              <Icon name="info" size="sm" className="text-info mt-0.5" />
              <div>
                <Typography variant="subtitle2" className="text-info font-medium">
                  {t('business:order.selectCarrierRequired', 'Vui lòng chọn nhà vận chuyển')}
                </Typography>
                <Typography variant="body2" className="text-info/80 mt-1">
                  {t('business:order.selectCarrierRequiredDescription', 'Bạn cần chọn nhà vận chuyển ưu tiên để hệ thống có thể tính phí vận chuyển tự động.')}
                </Typography>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PreferredCarrierSelector;
