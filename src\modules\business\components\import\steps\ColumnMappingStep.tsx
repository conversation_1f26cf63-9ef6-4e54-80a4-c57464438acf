import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Table,
  Icon,
  Select,
  Checkbox,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  ExcelData,
  ColumnMapping,
  CustomerField,
  CustomerFieldKey,
} from '../../../types/customer-import.types';

interface ColumnMappingStepProps {
  excelData: ExcelData;
  existingMappings?: ColumnMapping[];
  onMappingComplete: (mappings: ColumnMapping[]) => void;
  onMappingUpdate?: (mappings: ColumnMapping[]) => void;
  onGoBack?: () => void;
}

/**
 * Component cho bước mapping columns Excel với trường khách hàng
 */
const ColumnMappingStep: React.FC<ColumnMappingStepProps> = ({
  excelData,
  existingMappings,
  onMappingUpdate,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Danh sách trường khách hàng có thể map
  const customerFields: CustomerField[] = useMemo(
    () => {
      const fields: CustomerField[] = [
        {
          key: CustomerFieldKey.NAME,
          label: t('business:customer.form.name'),
          type: 'text' as const,
          required: true,
          isCustomField: false,
        },
        {
          key: CustomerFieldKey.EMAIL,
          label: t('business:customer.form.email'),
          type: 'email' as const,
          required: false,
          isCustomField: false,
        },
        {
          key: CustomerFieldKey.PHONE,
          label: t('business:customer.form.phone'),
          type: 'phone' as const,
          required: true,
          isCustomField: false,
        },
        {
          key: CustomerFieldKey.ADDRESS,
          label: t('business:customer.form.address'),
          type: 'text' as const,
          required: false,
          isCustomField: false,
        },
        {
          key: CustomerFieldKey.TAGS,
          label: t('business:customer.form.tags'),
          type: 'text' as const,
          required: false,
          isCustomField: false,
        },
        // TODO: Thêm custom fields từ API
      ];
      return fields;
    },
    [t]
  );

  // State cho mappings - sử dụng từ props nếu có, nếu không thì để trống để người dùng tự ánh xạ
  const [mappings, setMappings] = useState<ColumnMapping[]>(() => {
    // Nếu đã có mappings từ import state thì sử dụng
    if (existingMappings && existingMappings.length > 0) {
      return existingMappings;
    }

    // Không auto-mapping, để người dùng tự ánh xạ
    return excelData.headers.map(header => ({
      excelColumn: header,
      customerField: '', // Để trống để người dùng tự chọn
      isRequired: false,
    }));
  });

  // State cho checkbox xem trước dữ liệu
  const [showPreview, setShowPreview] = useState(false);

  // Sync với existingMappings khi props thay đổi
  useEffect(() => {
    if (existingMappings && existingMappings.length > 0) {
      setMappings(existingMappings);
    }
  }, [existingMappings]);

  // Đảm bảo mappings được save khi component unmount hoặc khi mappings thay đổi
  useEffect(() => {
    if (onMappingUpdate && mappings.length > 0) {
      onMappingUpdate(mappings);
    }
  }, [mappings, onMappingUpdate]);

  // Cập nhật mapping cho một cột
  const updateMapping = (excelColumn: string, customerField: string) => {
    const newMappings = mappings.map(mapping =>
      mapping && mapping.excelColumn === excelColumn
        ? {
            ...mapping,
            customerField,
            isRequired: customerFields.find(f => f.key === customerField)?.required || false,
          }
        : mapping
    );
    setMappings(newMappings);

    // Gọi callback để update parent state nếu có
    if (onMappingUpdate) {
      onMappingUpdate(newMappings);
    }
  };

  // Kiểm tra validation tổng thể
  const globalValidationErrors = useMemo(() => {
    const errors: string[] = [];

    // Kiểm tra trường bắt buộc
    const requiredFields = customerFields.filter(f => f.required);
    requiredFields.forEach(field => {
      const isMapped = mappings.some(m => m && m.customerField === field.key);
      if (!isMapped) {
        errors.push(
          t('business:customer.import.mapping.errors.requiredFieldMissing', { field: field.label })
        );
      }
    });

    return errors;
  }, [mappings, customerFields, t]);



  // Columns cho table preview
  const previewColumns: TableColumn<Record<string, unknown>>[] = [
    {
      key: 'index',
      title: '#',
      render: (_, __, index) => index + 1,
      width: 50,
    },
    ...excelData.headers.map(header => ({
      key: header,
      title: header,
      dataIndex: header,
      render: (value: unknown) => {
        const strValue = value ? String(value) : '';
        return strValue.length > 50 ? `${strValue.substring(0, 50)}...` : strValue;
      },
    })),
  ];

  // Data cho table preview (tất cả dữ liệu để có phân trang)
  const previewData = useMemo(() => {
    return excelData.rows.map((row, index) => {
      const rowData: Record<string, unknown> = { id: index };
      excelData.headers.forEach((header, headerIndex) => {
        rowData[header] = row[headerIndex];
      });
      return rowData;
    });
  }, [excelData]);

  return (
    <div className="w-full space-y-6">
      {/* File info */}
      <div className="p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center space-x-4">
          <Icon name="file-text" size="sm" />
          <div>
            <Typography variant="body2" className="font-medium">
              {excelData.fileName}
            </Typography>
            <Typography variant="body2" className="text-muted text-xs">
              {excelData.headers.length} cột, {excelData.rows.length} dòng
            </Typography>
          </div>
        </div>
      </div>

      {/* Column Mapping */}
      <div className="p-6 bg-card rounded-lg">
        <Typography variant="h6" className="mb-4">
          {t('business:customer.import.mapping.columnMapping')}
        </Typography>

        <div className="space-y-4">
          {excelData.headers.map(header => {
            const mapping = mappings.find(m => m && m.excelColumn === header);
            const selectedField = customerFields.find(f => f.key === mapping?.customerField);

            // Kiểm tra duplicate mapping
            const isDuplicate =
              mapping?.customerField &&
              mappings.filter(m => m && m.customerField === mapping.customerField).length > 1;

            const selectOptions = [
              { value: '', label: t('business:customer.import.mapping.skipColumn') },
              ...customerFields.map(field => ({
                value: field.key,
                label: `${field.label}${field.required ? ' *' : ''}`,
              })),
            ];

            return (
              <div key={header} className="flex items-center space-x-4 p-3 bg-muted/30 rounded-lg">
                <div className="flex-1">
                  <Typography variant="body2" className="font-medium">
                    {header}
                  </Typography>
                  <Typography variant="body2" className="text-muted text-xs">
                    Excel Column
                  </Typography>
                </div>

                <Icon name="arrow-right" size="sm" className="text-muted" />

                <div className="flex-1">
                  <Select
                    value={mapping?.customerField || ''}
                    onChange={value => updateMapping(header, value as string)}
                    placeholder={t('business:customer.import.mapping.skipColumn')}
                    {...(isDuplicate && {
                      error: t('business:customer.import.mapping.errors.duplicateMapping')
                    })}
                    options={selectOptions}
                  />
                  {selectedField?.required && !isDuplicate && (
                    <Typography variant="body2" className="text-xs text-orange-600 mt-1">
                      {t('business:customer.import.mapping.requiredField')}
                    </Typography>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Global Validation Errors */}
      {globalValidationErrors.length > 0 && (
        <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Icon name="alert-circle" size="sm" className="text-red-500 mt-0.5" />
            <div>
              <Typography variant="body2" className="font-medium text-red-700 mb-2">
                {t('business:customer.import.mapping.validationErrors')}
              </Typography>
              <ul className="space-y-1">
                {globalValidationErrors.map((error: string, index: number) => (
                  <li key={index} className="text-sm text-red-600">
                    • {error}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Preview Data */}
      <div className="p-6 bg-card rounded-lg">
        <Checkbox checked={showPreview} onChange={setShowPreview} label={t('business:customer.import.mapping.previewData')} />

        {showPreview && (
          <Table
            columns={previewColumns}
            data={previewData}
            rowKey="id"
            pagination={true}
            size="sm"
          />
        )}
      </div>


    </div>
  );
};

export default ColumnMappingStep;
