import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho trạng thái kho ảo
 */
export enum VirtualWarehouseStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

/**
 * Enum cho loại kho ảo
 */
export enum VirtualWarehouseType {
  VIRTUAL = 'VIRTUAL',
}

/**
 * DTO cho item trong danh sách kho ảo
 */
export interface VirtualWarehouseListItemDto {
  warehouseId: number;
  name: string;
  description?: string;
  type: VirtualWarehouseType;
  status: VirtualWarehouseStatus;
  associatedSystem?: string;
  purpose?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO cho chi tiết kho ảo
 */
export interface VirtualWarehouseDetailDto extends VirtualWarehouseListItemDto {
  // Có thể thêm các trường chi tiết khác nếu cần
  totalFiles?: number;
  totalSize?: number;
}

/**
 * DTO cho tạo mới kho ảo
 */
export interface CreateVirtualWarehouseDto {
  associatedSystem?: string;
  purpose?: string;
}

/**
 * DTO cho cập nhật kho ảo
 */
export interface UpdateVirtualWarehouseDto {
  name?: string;
  description?: string;
  associatedSystem?: string;
  purpose?: string;
  status?: VirtualWarehouseStatus;
}

/**
 * DTO cho query kho ảo
 */
export interface VirtualWarehouseQueryParams extends QueryDto {
  status?: VirtualWarehouseStatus;
  type?: VirtualWarehouseType;
}

/**
 * Form values cho tạo kho ảo
 */
export interface CreateVirtualWarehouseFormValues {
  warehouseId: number;
  associatedSystem?: string;
  purpose?: string;
}

/**
 * Form values cho chỉnh sửa kho ảo
 */
export interface EditVirtualWarehouseFormValues {
  associatedSystem?: string;
  purpose?: string;
}
