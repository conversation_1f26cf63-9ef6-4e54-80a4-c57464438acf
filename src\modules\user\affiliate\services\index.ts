import {
  getAffiliateOverview,
  requestWithdrawal,
  convertPoints,
  getAffiliateOrders,
  getWithdrawalHistory,
  getAffiliateCustomers,
  getAffiliateContracts,
} from '../api';

/**
 * Services layer cho affiliate - xử lý logic nghiệp vụ
 */

// Service lấy dữ liệu tổng quan với xử lý lỗi
export const getAffiliateOverviewService = async () => {
  try {
    const data = await getAffiliateOverview();
    
    // Xử lý dữ liệu nếu cần
    if (!data.stats) {
      throw new Error('Không thể lấy dữ liệu thống kê');
    }
    
    return data;
  } catch (error) {
    console.error('Error fetching affiliate overview:', error);
    throw error;
  }
};

// Service yêu cầu rút tiền với validation
export const requestWithdrawalService = async (amount: number) => {
  if (amount <= 0) {
    throw new Error('Số tiền rút phải lớn hơn 0');
  }

  try {
    return await requestWithdrawal(amount);
  } catch (error) {
    console.error('Error requesting withdrawal:', error);
    throw error;
  }
};

// Service đổi point với validation
export const convertPointsService = async (points: number) => {
  if (points <= 0) {
    throw new Error('Số point phải lớn hơn 0');
  }
  
  try {
    return await convertPoints(points);
  } catch (error) {
    console.error('Error converting points:', error);
    throw error;
  }
};

// Service lấy đơn hàng với default params
export const getAffiliateOrdersService = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  const defaultParams = {
    page: 1,
    limit: 10,
    ...params,
  };
  
  try {
    return await getAffiliateOrders(defaultParams);
  } catch (error) {
    console.error('Error fetching affiliate orders:', error);
    throw error;
  }
};

// Service lấy lịch sử rút tiền với default params
export const getWithdrawalHistoryService = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  const defaultParams = {
    page: 1,
    limit: 10,
    ...params,
  };
  
  try {
    return await getWithdrawalHistory(defaultParams);
  } catch (error) {
    console.error('Error fetching withdrawal history:', error);
    throw error;
  }
};

// Service lấy khách hàng với default params
export const getAffiliateCustomersService = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
}) => {
  const defaultParams = {
    page: 1,
    limit: 10,
    ...params,
  };

  try {
    return await getAffiliateCustomers(defaultParams);
  } catch (error) {
    console.error('Error fetching affiliate customers:', error);
    throw error;
  }
};

// Service lấy hợp đồng với default params
export const getAffiliateContractsService = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  const defaultParams = {
    page: 1,
    limit: 10,
    ...params,
  };

  try {
    return await getAffiliateContracts(defaultParams);
  } catch (error) {
    console.error('Error fetching affiliate contracts:', error);
    throw error;
  }
};
