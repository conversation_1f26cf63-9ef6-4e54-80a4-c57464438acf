import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useWatch } from 'react-hook-form';
import {
  Form,
  FormItem,
  Input,
  IconCard,
  Select,
  Card,
  Textarea,
  ConditionalField,
  DatePickerFormField,
  Checkbox,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { z } from 'zod';
import { ValidationSchemas } from '@/shared/validation/schemas';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail, CreateCustomFieldData } from '../../services/custom-field.service';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
  isSubmitting?: boolean;
}

// Đ<PERSON>nh nghĩa các pattern phổ biến với key để đa ngôn ngữ
const COMMON_PATTERN_KEYS = [
  { key: 'email', value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' },
  { key: 'phoneVN', value: '^(\\+84|0)[0-9]{9,10}$' },
  { key: 'phoneIntl', value: '^\\+[1-9]\\d{1,14}$' },
  { key: 'postalCodeVN', value: '^[0-9]{5,6}$' },
  { key: 'lettersOnly', value: '^[a-zA-Z]+$' },
  { key: 'numbersOnly', value: '^[0-9]+$' },
  { key: 'alphanumeric', value: '^[a-zA-Z0-9]+$' },
  { key: 'noSpecialChars', value: '^[a-zA-Z0-9\\s]+$' },
  { key: 'url', value: '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$' },
  { key: 'ipv4', value: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$' },
  { key: 'strongPassword', value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$' },
  { key: 'vietnameseName', value: '^[a-zA-ZÀ-ỹ\\s]+$' },
  { key: 'studentId', value: '^[A-Z]{2}[0-9]{6}$' },
  { key: 'nationalId', value: '^[0-9]{9,12}$' },
  { key: 'taxCode', value: '^[0-9]{10,13}$' },
  { key: 'dateFormat', value: '^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[012])\\/(19|20)\\d\\d$' },
  { key: 'timeFormat', value: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
  { key: 'hexColor', value: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$' },
  { key: 'base64', value: '^[A-Za-z0-9+\\/]*={0,2}$' },
  { key: 'uuid', value: '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$' },
  { key: 'filename', value: '^[^<>:"/\\\\|?*]+\\.[a-zA-Z0-9]+$' },
  { key: 'urlSlug', value: '^[a-z0-9]+(?:-[a-z0-9]+)*$' },
  { key: 'variableName', value: '^[a-zA-Z_$][a-zA-Z0-9_$]*$' },
  { key: 'creditCard', value: '^[0-9]{13,19}$' },
  { key: 'qrCode', value: '^[A-Z0-9 $%*+\\-./:]+$' },
  { key: 'gpsCoordinate', value: '^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}$' },
  { key: 'rgbColor', value: '^rgb\\(([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\)$' },
  { key: 'domain', value: '^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$' },
  { key: 'decimal', value: '^\\d+(\\.\\d{1,2})?$' },
  { key: 'barcode', value: '^[0-9]{8,14}$' },
];

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const { mutateAsync: createCustomField, isPending: isCreating } = useCreateCustomField();
  const { mutateAsync: updateCustomField, isPending: isUpdating } = useUpdateCustomField();

  const formRef = useRef<FormRef<Record<string, unknown>> | null>(null);
  const patternInputRef = useRef<HTMLInputElement>(null);

  // Debug component để xem giá trị form
  const DebugFormValues = () => {
    const typeValue = useWatch({ name: 'type' });
    const patternValue = useWatch({ name: 'validation.pattern' });

    console.log('🔍 Current form values:', { type: typeValue, pattern: patternValue });

    return (
      <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded text-xs">
        <strong>Debug:</strong> type="{typeValue}", pattern="{patternValue}"
      </div>
    );
  };

  // State cho advanced settings - tự động hiển thị nếu có dữ liệu validation
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(() => {
    if (!initialData) return false;

    const validation = initialData.configJson?.['validation'] as Record<string, unknown> | undefined;
    const hasValidation = validation && (
      validation['minLength'] ||
      validation['maxLength'] ||
      validation['pattern'] ||
      validation['min'] ||
      validation['max']
    );

    const hasAdvancedConfig =
      initialData.configJson?.['placeholder'] ||
      initialData.configJson?.['defaultValue'] ||
      initialData.configJson?.['description'] ||
      hasValidation;

    return Boolean(hasAdvancedConfig);
  });

  // Schema cho form
  const customFieldSchema = z.object({
    id: z.string().min(1, t('business:customField.form.idRequired')),
    displayName: z.string().min(1, t('business:customField.form.displayNameRequired')),
    label: z.string().optional(), // Nhãn không bắt buộc
    type: z.string().min(1, t('business:customField.form.typeRequired')),
    placeholder: z.string().optional(),
    defaultValue: z.union([
      z.string(),
      ValidationSchemas.flexibleDate({ t })
    ]).optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    id: '',
    displayName: '',
    type: 'text',
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    console.log('🔍 Form values on submit:', values);
    try {
      // Xử lý defaultValue dựa trên type
      let processedDefaultValue: string | undefined;
      if (values.defaultValue) {
        if (values.type === 'date') {
          // Nếu là date và defaultValue là Date object, chuyển thành string
          if (values.defaultValue instanceof Date) {
            processedDefaultValue = values.defaultValue.toISOString().split('T')[0];
          } else {
            // Nếu đã là string, giữ nguyên
            processedDefaultValue = String(values.defaultValue);
          }
        } else {
          // Các type khác chuyển thành string
          processedDefaultValue = String(values.defaultValue);
        }
      }

      // Chuẩn bị dữ liệu cho API - displayName sẽ được truyền xuống backend là label
      const formData: CreateCustomFieldData = {
        // Bỏ trường component - API không yêu cầu
        config: {
          id: String(values.id),
          label: String(values.displayName), // displayName truyền xuống backend là label
          displayName: String(values.displayName),
          type: String(values.type),
          ...(values.placeholder && { placeholder: String(values.placeholder) }),
          ...(processedDefaultValue !== undefined && { defaultValue: processedDefaultValue }),
          ...(values.description && { description: String(values.description) }),
          validation: {
            ...(values.validation?.minLength && { minLength: Number(values.validation.minLength) }),
            ...(values.validation?.maxLength && { maxLength: Number(values.validation.maxLength) }),
            ...(values.validation?.pattern && { pattern: String(values.validation.pattern) }),
          },
        },
      };

      // Thêm options nếu có (chỉ áp dụng cho select, radio, checkbox)
      if (values.options) {
        try {
          const optionsString = String(values.options);
          let optionsArray;

          if (optionsString.includes('{')) {
            // Nếu là JSON format
            optionsArray = JSON.parse(optionsString);
          } else {
            // Xử lý format "label|value" cho mỗi dòng
            optionsArray = optionsString
              .split('\n')
              .map(line => line.trim())
              .filter(line => line.length > 0)
              .map(line => {
                if (line.includes('|')) {
                  // Format: "label|value"
                  const [label, value] = line.split('|').map(part => part.trim());
                  return { label, value };
                } else {
                  // Fallback: sử dụng cùng giá trị cho cả label và value
                  return {
                    label: line,
                    value: line.toLowerCase().replace(/\s+/g, '_'),
                  };
                }
              });
          }

          if (optionsString.includes('{')) {
            // Parse từ JSON format
            optionsArray = JSON.parse(optionsString);
          } else if (optionsString.includes('|') && optionsString.includes('\n')) {
            // Parse từ format "a|1\nb|2" thành [{"label": "a", "value": "1"}, {"label": "b", "value": "2"}]
            optionsArray = optionsString
              .split('\n')
              .map(line => line.trim())
              .filter(line => line.length > 0)
              .map(line => {
                const parts = line.split('|');
                if (parts.length >= 2 && parts[0] && parts[1]) {
                  return {
                    label: parts[0].trim(),
                    value: parts[1].trim(),
                  };
                } else {
                  // Fallback nếu không có dấu |
                  return {
                    label: line.trim(),
                    value: line.trim().toLowerCase().replace(/\s+/g, '_'),
                  };
                }
              });
          } else {
            // Parse từ danh sách ngăn cách bởi dấu phẩy
            optionsArray = optionsString.split(',').map(option => ({
              label: option.trim(),
              value: option.trim().toLowerCase().replace(/\s+/g, '_'),
            }));
          }

          (formData.config as Record<string, unknown>)['options'] = optionsArray;
        } catch (error) {
          console.error('Error parsing options:', error);
        }
      }

      if (initialData) {
        // Cập nhật trường tùy chỉnh
        await updateCustomField({
          id: initialData.id,
          data: formData,
        });
      } else {
        // Tạo trường tùy chỉnh mới
        await createCustomField(formData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };



  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    console.log('🔍 Processing initialData:', initialData);

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.['validation'] as Record<string, unknown> | undefined;

    // Xử lý defaultValue dựa trên type
    let processedDefaultValue: string | Date | undefined;
    const rawDefaultValue = initialData.configJson?.['defaultValue'];

    if (rawDefaultValue) {
      if (initialData.type === 'date') {
        // Nếu là date, chuyển đổi thành Date object hoặc string
        try {
          processedDefaultValue = typeof rawDefaultValue === 'string'
            ? rawDefaultValue
            : new Date(rawDefaultValue as string).toISOString().split('T')[0];
        } catch {
          processedDefaultValue = '';
        }
      } else {
        // Các type khác giữ nguyên dạng string
        processedDefaultValue = String(rawDefaultValue);
      }
    } else {
      processedDefaultValue = '';
    }

    const result = {
      id: initialData.configId || '', // ✅ Sử dụng configId từ API response
      displayName: (initialData.configJson?.['displayName'] as string) || initialData.label || '',
      type: initialData.type,
      placeholder: (initialData.configJson?.['placeholder'] as string) || '',
      defaultValue: processedDefaultValue,
      description: (initialData.configJson?.['description'] as string) || '',
      validation: {
        minLength: validation?.['minLength'] ? String(validation['minLength']) : '',
        maxLength: validation?.['maxLength'] ? String(validation['maxLength']) : '',
        pattern: validation?.['pattern'] ? String(validation['pattern']) : '',
      },
      options: initialData.configJson?.['options']
        ? (() => {
            try {
              const optionsData = initialData.configJson['options'];
              // Nếu là array of objects với label và value, chuyển thành format label|value
              if (Array.isArray(optionsData) && optionsData.length > 0 &&
                  typeof optionsData[0] === 'object' &&
                  'label' in optionsData[0] && 'value' in optionsData[0]) {
                return optionsData
                  .map((option: { label: string; value: string }) => `${option.label}|${option.value}`)
                  .join('\n');
              }
              // Nếu không phải format object array, giữ nguyên JSON string
              return JSON.stringify(optionsData);
            } catch (error) {
              console.error('Error processing options for display:', error);
              return JSON.stringify(initialData.configJson['options']);
            }
          })()
        : '',
    };

    console.log('🔍 getInitialValues result:', result);
    return result;
  };

  return (
    <Card title={initialData ? t('business:customField.edit') : t('business:customField.add')}>
      <Form
        ref={formRef}
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-6"
      >
        {/* Thông tin cơ bản */}
        <div className="space-y-4 mb-6">
          <FormItem
            name="type"
            label={t('business:customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('business:customField.types.text') },
                { value: 'number', label: t('business:customField.types.number') },
                { value: 'boolean', label: t('business:customField.types.boolean') },
                { value: 'date', label: t('business:customField.types.date') },
                { value: 'select', label: t('business:customField.types.select') },
                { value: 'object', label: t('business:customField.types.object') },
                { value: 'array', label: t('business:customField.types.array') },
              ]}
            />
          </FormItem>

          <FormItem
            name="id"
            label={t('business:customField.form.fieldIdLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:customField.form.fieldIdPlaceholder')}
              pattern="^[a-zA-Z0-9_-]+$"
            />
          </FormItem>

          <FormItem
            name="displayName"
            label={t('business:customField.form.displayNameLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:customField.form.displayNamePlaceholder')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('business:customField.form.description')}
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('business:customField.form.descriptionPlaceholder')}
            />
          </FormItem>

          {/* Tùy chọn cho kiểu dữ liệu select */}
          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.EQUALS,
              value: 'select',
            }}
          >
            <FormItem
              name="options"
              label={t('business:customField.form.options')}
            >
              <Textarea
                fullWidth
                rows={6}
                placeholder="Nam|0&#10;Nữ|1&#10;Khác|2"
              />
            </FormItem>
          </ConditionalField>
        </div>

        {/* Debug info */}
        <DebugFormValues />

        {/* Checkbox để hiện/ẩn cài đặt nâng cao */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            checked={showAdvancedSettings}
            onChange={setShowAdvancedSettings}
            label={t('business:customField.form.showAdvancedSettings', 'Hiển thị cài đặt nâng cao')}
          />
        </div>

        {/* Cài đặt nâng cao */}
        {showAdvancedSettings && (
          <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <FormItem
              name="placeholder"
              label={t('business:customField.form.placeholder')}
            >
              <Input fullWidth placeholder={t('business:customField.form.placeholderPlaceholder')} />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'number',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  type="number"
                  placeholder={t('business:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'boolean',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <Select
                  fullWidth
                  placeholder={t('business:customField.form.booleanDefaultPlaceholder')}
                  options={[
                    { value: 'true', label: t('business:customField.booleanValues.true') },
                    { value: 'false', label: t('business:customField.booleanValues.false') },
                  ]}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'date',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <DatePickerFormField
                  fullWidth
                  placeholder={t('business:customField.form.dateDefaultPlaceholder')}
                  format="dd/MM/yyyy"
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.IN,
                value: ['text', 'select', 'object', 'array'],
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('business:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  placeholder={t('business:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Validation cho Text */}
              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.minLength"
                  label={t('business:customField.validation.minLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="0" />
                </FormItem>
              </ConditionalField>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.maxLength"
                  label={t('business:customField.validation.maxLength')}
                >
                  <Input fullWidth type="number" min="0" placeholder="100" />
                </FormItem>
              </ConditionalField>

              {/* Validation cho Number */}
              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'number',
                }}
              >
                <FormItem
                  name="validation.min"
                  label={t('business:customField.validation.min')}
                >
                  <Input fullWidth type="number" placeholder="0" />
                </FormItem>
              </ConditionalField>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'number',
                }}
              >
                <FormItem
                  name="validation.max"
                  label={t('business:customField.validation.max')}
                >
                  <Input fullWidth type="number" placeholder="100" />
                </FormItem>
              </ConditionalField>
            </div>

              <ConditionalField
                condition={{
                  field: 'type',
                  type: ConditionType.EQUALS,
                  value: 'text',
                }}
              >
                <FormItem
                  name="validation.pattern"
                  label={t('business:customField.validation.pattern')}
                >
                  <div className="space-y-2">
                    <Input
                      ref={patternInputRef}
                      fullWidth
                      placeholder="^[A-Za-z0-9]+$"
                    />
                    <div className="text-sm text-gray-600">
                      <p className="font-medium mb-2">{t('business:customField.form.patternSuggestions')}</p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 max-h-32 overflow-y-auto">
                        {COMMON_PATTERN_KEYS.map((pattern, index) => (
                          <button
                            key={index}
                            type="button"
                            className="text-left text-xs p-1 hover:bg-gray-100 rounded truncate"
                            title={pattern.value}
                            onClick={() => {
                              if (formRef.current) {
                                // Cập nhật form value
                                formRef.current.setValues({
                                  'validation.pattern': pattern.value
                                });
                              }
                            }}
                          >
                            <span className="font-medium text-blue-600">{t(`business:customField.patterns.${pattern.key}`)}:</span>
                            <span className="ml-1 text-gray-500 font-mono">{pattern.value.substring(0, 20)}...</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </FormItem>
              </ConditionalField>


          </div>
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={isSubmitting || isCreating || isUpdating}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={isSubmitting || isCreating || isUpdating}
            isLoading={isSubmitting || isCreating || isUpdating}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
