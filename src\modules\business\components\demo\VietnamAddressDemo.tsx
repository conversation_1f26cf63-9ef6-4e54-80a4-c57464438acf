import React, { useState } from 'react';
import { useAddressOptions } from '../../hooks/useVietnamAddress';
import { Select, FormItem, Typography, Card } from '@/shared/components/common';

/**
 * Demo component để test Vietnam Address API
 */
const VietnamAddressDemo: React.FC = () => {
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [selectedWard, setSelectedWard] = useState<string>('');

  const addressOptions = useAddressOptions(selectedProvince, selectedDistrict);

  const handleProvinceChange = (value: string | string[] | number | number[]) => {
    const stringValue = Array.isArray(value) ? value[0]?.toString() || '' : value.toString();
    setSelectedProvince(stringValue);
    setSelectedDistrict('');
    setSelectedWard('');
  };

  const handleDistrictChange = (value: string | string[] | number | number[]) => {
    const stringValue = Array.isArray(value) ? value[0]?.toString() || '' : value.toString();
    setSelectedDistrict(stringValue);
    setSelectedWard('');
  };

  const handleWardChange = (value: string | string[] | number | number[]) => {
    const stringValue = Array.isArray(value) ? value[0]?.toString() || '' : value.toString();
    setSelectedWard(stringValue);
  };

  return (
    <Card className="p-6 max-w-4xl mx-auto">
      <Typography variant="h4" className="mb-6">
        Demo Vietnam Address API
      </Typography>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <FormItem label="Tỉnh/Thành phố">
            <Select
              value={selectedProvince}
              onChange={handleProvinceChange}
              placeholder={addressOptions.provinces.isLoading ? 'Đang tải...' : 'Chọn tỉnh/thành phố'}
              options={addressOptions.provinces.data}
              loading={addressOptions.provinces.isLoading}
              fullWidth
            />
          </FormItem>
          {addressOptions.provinces.error && (
            <Typography variant="caption" className="text-destructive mt-1">
              Lỗi: {addressOptions.provinces.error.message}
            </Typography>
          )}
        </div>

        <div>
          <FormItem label="Quận/Huyện">
            <Select
              value={selectedDistrict}
              onChange={handleDistrictChange}
              placeholder={
                !selectedProvince
                  ? 'Chọn tỉnh/thành phố trước'
                  : addressOptions.districts.isLoading
                    ? 'Đang tải...'
                    : 'Chọn quận/huyện'
              }
              options={addressOptions.districts.data}
              loading={addressOptions.districts.isLoading}
              disabled={!selectedProvince}
              fullWidth
            />
          </FormItem>
          {addressOptions.districts.error && (
            <Typography variant="caption" className="text-destructive mt-1">
              Lỗi: {addressOptions.districts.error.message}
            </Typography>
          )}
        </div>

        <div>
          <FormItem label="Phường/Xã">
            <Select
              value={selectedWard}
              onChange={handleWardChange}
              placeholder={
                !selectedDistrict
                  ? 'Chọn quận/huyện trước'
                  : addressOptions.wards.isLoading
                    ? 'Đang tải...'
                    : 'Chọn phường/xã'
              }
              options={addressOptions.wards.data}
              loading={addressOptions.wards.isLoading}
              disabled={!selectedDistrict}
              fullWidth
            />
          </FormItem>
          {addressOptions.wards.error && (
            <Typography variant="caption" className="text-destructive mt-1">
              Lỗi: {addressOptions.wards.error.message}
            </Typography>
          )}
        </div>
      </div>

      <div className="bg-muted p-4 rounded-lg">
        <Typography variant="h6" className="mb-2">
          Kết quả:
        </Typography>
        <div className="space-y-1">
          <Typography variant="body2">
            <strong>Tỉnh/Thành phố:</strong> {selectedProvince || 'Chưa chọn'}
          </Typography>
          <Typography variant="body2">
            <strong>Quận/Huyện:</strong> {selectedDistrict || 'Chưa chọn'}
          </Typography>
          <Typography variant="body2">
            <strong>Phường/Xã:</strong> {selectedWard || 'Chưa chọn'}
          </Typography>
        </div>
      </div>

      <div className="mt-6 space-y-4">
        <Typography variant="h6">
          Thống kê API:
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-card p-3 rounded border">
            <Typography variant="subtitle2" className="text-muted-foreground">
              Tỉnh/Thành phố
            </Typography>
            <Typography variant="body1">
              {addressOptions.provinces.isLoading ? 'Đang tải...' : `${addressOptions.provinces.data.length} tỉnh/thành phố`}
            </Typography>
          </div>
          <div className="bg-card p-3 rounded border">
            <Typography variant="subtitle2" className="text-muted-foreground">
              Quận/Huyện
            </Typography>
            <Typography variant="body1">
              {addressOptions.districts.isLoading ? 'Đang tải...' : `${addressOptions.districts.data.length} quận/huyện`}
            </Typography>
          </div>
          <div className="bg-card p-3 rounded border">
            <Typography variant="subtitle2" className="text-muted-foreground">
              Phường/Xã
            </Typography>
            <Typography variant="body1">
              {addressOptions.wards.isLoading ? 'Đang tải...' : `${addressOptions.wards.data.length} phường/xã`}
            </Typography>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default VietnamAddressDemo;
