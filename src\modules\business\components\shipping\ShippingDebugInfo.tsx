import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Icon,
} from '@/shared/components/common';

interface CarrierQuote {
  carrier: 'GHN' | 'GHTK';
  fee: number;
  serviceType: string;
  estimatedDeliveryTime?: string;
  isAvailable: boolean;
  error?: string;
}

interface ShippingDebugInfoProps {
  quotes: CarrierQuote[];
  lastRequest?: Record<string, unknown>;
  className?: string;
}

/**
 * Component debug thông tin shipping để troubleshoot
 */
const ShippingDebugInfo: React.FC<ShippingDebugInfoProps> = ({
  quotes,
  lastRequest,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Chỉ hiển thị khi có lỗi hoặc trong development mode
  const hasErrors = quotes.some(q => !q.isAvailable);
  const isDevelopment = process.env['NODE_ENV'] === 'development';
  
  if (!hasErrors && !isDevelopment) {
    return null;
  }

  return (
    <Card className={`border-orange-200 bg-orange-50 ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Icon name="bug" size="sm" className="text-orange-600" />
            <Typography variant="subtitle2" className="text-orange-800">
              Debug Thông tin Vận chuyển
            </Typography>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <Icon 
              name={isExpanded ? "chevron-up" : "chevron-down"} 
              size="sm" 
            />
          </Button>
        </div>

        {isExpanded && (
          <div className="space-y-4">
            {/* Request Information */}
            {lastRequest && (
              <div>
                <Typography variant="subtitle2" className="mb-2 text-orange-800">
                  Request cuối cùng:
                </Typography>
                <div className="bg-white p-3 rounded border text-xs">
                  <pre className="whitespace-pre-wrap overflow-x-auto">
                    {JSON.stringify(lastRequest, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Quotes Information */}
            <div>
              <Typography variant="subtitle2" className="mb-2 text-orange-800">
                Kết quả từ các nhà vận chuyển:
              </Typography>
              <div className="space-y-2">
                {quotes.map((quote) => (
                  <div 
                    key={quote.carrier}
                    className={`p-3 rounded border ${
                      quote.isAvailable 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="subtitle2" className={
                        quote.isAvailable ? 'text-green-800' : 'text-red-800'
                      }>
                        {quote.carrier}
                      </Typography>
                      <Typography variant="caption" className={
                        quote.isAvailable ? 'text-green-600' : 'text-red-600'
                      }>
                        {quote.isAvailable ? 'Thành công' : 'Lỗi'}
                      </Typography>
                    </div>
                    
                    {quote.isAvailable ? (
                      <div className="text-xs space-y-1">
                        <div>Phí: {quote.fee.toLocaleString('vi-VN')} VND</div>
                        <div>Dịch vụ: {quote.serviceType}</div>
                        {quote.estimatedDeliveryTime && (
                          <div>Thời gian: {quote.estimatedDeliveryTime}</div>
                        )}
                      </div>
                    ) : (
                      <div className="text-xs text-red-600">
                        <strong>Lỗi:</strong> {quote.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Troubleshooting Tips */}
            <div>
              <Typography variant="subtitle2" className="mb-2 text-orange-800">
                Gợi ý khắc phục:
              </Typography>
              <div className="text-xs space-y-1 text-orange-700">
                <div>• Kiểm tra cấu hình API token cho GHN/GHTK</div>
                <div>• Đảm bảo địa chỉ giao hàng hợp lệ và đầy đủ</div>
                <div>• Kiểm tra sản phẩm có thông tin kích thước/trọng lượng</div>
                <div>• Xem console để biết thêm chi tiết lỗi</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ShippingDebugInfo;
